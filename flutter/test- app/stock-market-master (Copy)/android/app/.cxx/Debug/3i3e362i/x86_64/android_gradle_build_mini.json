{"buildFiles": ["/home/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/home/<USER>/Desktop/WorkingOn/flutter/stock-market/stock-market-master/android/app/.cxx/Debug/3i3e362i/x86_64", "clean"]], "buildTargetsCommandComponents": ["/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja", "-C", "/home/<USER>/Desktop/WorkingOn/flutter/stock-market/stock-market-master/android/app/.cxx/Debug/3i3e362i/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}