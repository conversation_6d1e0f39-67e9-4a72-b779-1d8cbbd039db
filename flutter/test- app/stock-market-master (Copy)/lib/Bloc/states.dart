import 'package:stock_market/Bloc/events.dart';
import 'package:stock_market/class/action.dart';

class StockMarketStates {}

class LoginState extends StockMarketStates {}

class RegisterState extends StockMarketStates {}

class MarketState extends StockMarketStates {}

class WalletState extends StockMarketStates {}

class LoadingState extends StockMarketStates {}

class SearchState extends StockMarketStates {}

class ActionDetailState extends StockMarketStates {
  final ActionStock? action;

  ActionDetailState({required this.action});
}

class DefaultState extends StockMarketStates {}

class TransitionState extends StockMarketStates {
  final StockMarketStates currentState;
  final ChangeStateWithTransition event;
  final StockMarketStates nextState;

  TransitionState({
    required this.currentState,
    required this.event,
    required this.nextState,
  });
}

class ErrorState extends StockMarketStates {
  final String message;
  ErrorState({required this.message});
}

// New navigation states for enhanced navigation
class HomeState extends StockMarketStates {}

class PortfolioState extends StockMarketStates {}

class WatchlistState extends StockMarketStates {}

class ProfileState extends StockMarketStates {}
