// events.dart

import 'package:stock_market/Bloc/states.dart';
import 'package:stock_market/class/action.dart';

enum StateApp {
  login,
  register,
  market,
  transition,
  wallet,
  actionDetails,
  search,
}

extension StateAppExtension on StateApp {
  StockMarketStates giveStateClass({ActionStock? action}) {
    switch (this) {
      case StateApp.login:
        return LoginState();
      case StateApp.register:
        return RegisterState();
      case StateApp.market:
        return MarketState();
      case StateApp.actionDetails:
        return ActionDetailState(action: action);
      case StateApp.wallet:
        return WalletState();
      case StateApp.search:
        return SearchState();
      default:
        return DefaultState(); // In case of unknown value
    }
  }
}

// Abstract class to define a generic event
abstract class StockMarketEvent {}

class ChangeStateWithTransition extends StockMarketEvent {
  final StateApp stateNext;
  final StateApp stateNow;
  final ActionStock? action;

  ChangeStateWithTransition({
    required this.stateNext,
    required this.stateNow,
    this.action,
  });
}

class ChangeState extends StockMarketEvent {
  final StateApp stateNext;

  ChangeState({required this.stateNext});
}

// Event to handle errors
class ErrorEvent extends StockMarketEvent {
  final String message;

  // Constructor to pass the error message
  ErrorEvent({required this.message});
}
