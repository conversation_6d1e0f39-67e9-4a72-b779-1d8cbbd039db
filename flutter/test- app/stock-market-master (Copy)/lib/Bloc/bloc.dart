// bloc.dart

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stock_market/db/db_shared.dart';
import 'events.dart';
import 'states.dart';

class MarketBloc extends Bloc<StockMarketEvent, StockMarketStates> {
  MarketBloc() : super(LoadingState()) {
    _initializeState();

    on<ChangeState>((event, emit) {
      emit(event.stateNext.giveStateClass());
    });

    on<ChangeStateWithTransition>((event, emit) async {
      emit(TransitionState(
        currentState: event.stateNow.giveStateClass(),
        event: event,
        nextState: event.stateNext.giveStateClass(action: event.action),
      ));

      await Future.delayed(const Duration(seconds: 1));

      emit(event.stateNext.giveStateClass(action: event.action));
    });

    on<ErrorEvent>((event, emit) {
      emit(ErrorState(message: event.message));
    });
  }

  Future<void> _initializeState() async {
    String user = await loadUserCookie();
    if (user.isNotEmpty) {
      add(ChangeStateWithTransition(
          stateNext: StateApp.market, stateNow: StateApp.login));
    } else {
      add(ChangeStateWithTransition(
          stateNext: StateApp.login, stateNow: StateApp.login));
    }
  }
}
