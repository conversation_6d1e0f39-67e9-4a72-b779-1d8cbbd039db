import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../Bloc/bloc.dart';
import '../Bloc/events.dart';
import '../Bloc/states.dart';
import '../theme/app_colors.dart';
import '../theme/app_dimensions.dart';
import '../theme/app_text_styles.dart';
import '../pages/market/market_widget.dart';
import '../pages/wallet/wallet_widget.dart';
import '../pages/search_bar.dart';
import '../pages/market/details_actions_widget.dart';
import '../pages/profile/profile_page.dart';
import '../db/db_shared.dart';
import 'styled_components.dart';

/// Main navigation widget with bottom navigation bar
class MainNavigationWidget extends StatefulWidget {
  const MainNavigationWidget({super.key});

  @override
  State<MainNavigationWidget> createState() => _MainNavigationWidgetState();
}

class _MainNavigationWidgetState extends State<MainNavigationWidget> {
  int _currentIndex = 0;

  // Navigation items configuration
  final List<NavigationItem> _navigationItems = [
    NavigationItem(
      icon: Icons.trending_up,
      activeIcon: Icons.trending_up,
      label: 'Market',
      state: StateApp.market,
    ),
    NavigationItem(
      icon: Icons.search_outlined,
      activeIcon: Icons.search,
      label: 'Search',
      state: StateApp.search,
    ),
    NavigationItem(
      icon: Icons.account_balance_wallet_outlined,
      activeIcon: Icons.account_balance_wallet,
      label: 'Portfolio',
      state: StateApp.wallet,
    ),
    NavigationItem(
      icon: Icons.person_outline,
      activeIcon: Icons.person,
      label: 'Profile',
      state: StateApp.profile,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MarketBloc, StockMarketStates>(
      builder: (context, state) {
        // Update current index based on state
        _updateCurrentIndex(state);

        return Scaffold(
          appBar: _buildAppBar(context, state),
          body: _buildBody(state),
          bottomNavigationBar: _buildBottomNavigationBar(context, state),
        );
      },
    );
  }

  void _updateCurrentIndex(StockMarketStates state) {
    if (state is MarketState) {
      _currentIndex = 0;
    } else if (state is SearchState) {
      _currentIndex = 1;
    } else if (state is WalletState) {
      _currentIndex = 2;
    } else if (state is ProfileState) {
      _currentIndex = 3;
    }
  }

  PreferredSizeWidget? _buildAppBar(BuildContext context, StockMarketStates state) {
    // Don't show app bar for action details (full screen)
    if (state is ActionDetailState) {
      return null;
    }

    String title = _getAppBarTitle(state);
    
    return AppBar(
      title: Text(title, style: AppTextStyles.appBarTitle),
      elevation: 0,
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.textOnPrimary,
      actions: _buildAppBarActions(context, state),
    );
  }

  String _getAppBarTitle(StockMarketStates state) {
    if (state is MarketState) return 'Market';
    if (state is SearchState) return 'Search Stocks';
    if (state is WalletState) return 'Portfolio';
    if (state is ProfileState) return 'Profile';
    return 'StockMarket';
  }

  List<Widget> _buildAppBarActions(BuildContext context, StockMarketStates state) {
    List<Widget> actions = [];

    // Add refresh button for market and search
    if (state is MarketState || state is SearchState) {
      actions.add(
        IconButton(
          onPressed: () {
            // Trigger refresh - you can implement this
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Refreshing data...')),
            );
          },
          icon: const Icon(Icons.refresh),
          tooltip: 'Refresh',
        ),
      );
    }

    // Add logout button
    actions.add(
      IconButton(
        onPressed: () => _showLogoutDialog(context),
        icon: Icon(
          Icons.exit_to_app,
          color: AppColors.error,
        ),
        tooltip: 'Logout',
      ),
    );

    return actions;
  }

  Widget _buildBody(StockMarketStates state) {
    if (state is ActionDetailState) {
      return ActionDetailsWidget(action: state.action);
    }

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: AppDimensions.animationMedium),
      switchInCurve: Curves.easeInOut,
      switchOutCurve: Curves.easeInOut,
      transitionBuilder: (Widget child, Animation<double> animation) {
        return FadeTransition(
          opacity: animation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0.1, 0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          ),
        );
      },
      child: _getPageForState(state),
    );
  }

  Widget _getPageForState(StockMarketStates state) {
    if (state is MarketState) {
      return const MarketPlace();
    } else if (state is SearchState) {
      return SearchBarMarket();
    } else if (state is WalletState) {
      return const WalletWidget();
    } else if (state is ProfileState) {
      return const ProfilePage();
    }
    
    // Default fallback
    return const MarketPlace();
  }

  Widget? _buildBottomNavigationBar(BuildContext context, StockMarketStates state) {
    // Hide bottom nav for action details
    if (state is ActionDetailState) {
      return null;
    }

    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: BottomNavigationBar(
        currentIndex: _currentIndex,
        type: BottomNavigationBarType.fixed,
        backgroundColor: AppColors.surface,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textHint,
        selectedLabelStyle: AppTextStyles.labelSmall.copyWith(
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: AppTextStyles.labelSmall,
        elevation: 0,
        onTap: (index) => _onNavigationTap(context, index),
        items: _navigationItems.map((item) {
          final isSelected = _navigationItems.indexOf(item) == _currentIndex;
          return BottomNavigationBarItem(
            icon: Container(
              padding: const EdgeInsets.symmetric(
                vertical: AppDimensions.paddingXS,
                horizontal: AppDimensions.paddingSM,
              ),
              decoration: isSelected
                  ? BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusSM),
                    )
                  : null,
              child: Icon(
                isSelected ? item.activeIcon : item.icon,
                size: AppDimensions.iconMD,
              ),
            ),
            label: item.label,
          );
        }).toList(),
      ),
    );
  }

  void _onNavigationTap(BuildContext context, int index) {
    if (index == _currentIndex) return; // Don't navigate to same tab

    final targetState = _navigationItems[index].state;
    BlocProvider.of<MarketBloc>(context).add(
      ChangeStateWithTransition(
        stateNext: targetState,
        stateNow: _getCurrentStateApp(),
      ),
    );
  }

  StateApp _getCurrentStateApp() {
    switch (_currentIndex) {
      case 0: return StateApp.market;
      case 1: return StateApp.search;
      case 2: return StateApp.wallet;
      case 3: return StateApp.profile;
      default: return StateApp.market;
    }
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusLG),
          ),
          title: Text(
            'Logout',
            style: AppTextStyles.headlineSmall,
          ),
          content: Text(
            'Are you sure you want to logout?',
            style: AppTextStyles.bodyMedium,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Cancel',
                style: AppTextStyles.labelLarge.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            StyledGradientButton(
              text: 'Logout',
              gradientColors: [AppColors.error, AppColors.error.withValues(alpha: 0.8)],
              onPressed: () async {
                Navigator.pop(context);
                await removeCookie();
                if (context.mounted) {
                  BlocProvider.of<MarketBloc>(context).add(
                    ChangeStateWithTransition(
                      stateNext: StateApp.login,
                      stateNow: _getCurrentStateApp(),
                    ),
                  );
                }
              },
            ),
          ],
        );
      },
    );
  }
}

/// Navigation item configuration
class NavigationItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;
  final StateApp state;

  NavigationItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
    required this.state,
  });
}


