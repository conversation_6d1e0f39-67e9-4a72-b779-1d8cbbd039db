import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stock_market/Bloc/bloc.dart';

import 'package:stock_market/Bloc/observer.dart';
import 'package:stock_market/Bloc/states.dart';
import 'package:stock_market/db/db_sql.dart';
import 'package:stock_market/pages/auth/login.dart';
import 'package:stock_market/pages/auth/register.dart';
import 'package:stock_market/pages/loading.dart';
import 'package:stock_market/theme/app_theme.dart';
import 'package:stock_market/widgets/main_navigation.dart';

Future<void> main() async {
  WidgetsFlutterBinding
      .ensureInitialized(); // Ensure initialization before runApp
  await DatabaseHelper.instance.initDatabase(); // Initialize database
  Bloc.observer = MyBlocObserver();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => MarketBloc(), // Provide the bloc here
      child: MaterialApp(
        title: 'StockMarket',
        theme: AppTheme.lightTheme,
        home: const PagesSwitcher(),
      ),
    );
  }
}

class PagesSwitcher extends StatelessWidget {
  const PagesSwitcher({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MarketBloc, StockMarketStates>(
      builder: (context, state) {
        // Handle authentication states
        if (state is TransitionState) {
          return const Scaffold(
            body: Center(child: LoadingWidget()),
          );
        } else if (state is LoginState) {
          return const LoginPage();
        } else if (state is RegisterState) {
          return const RegisterPage();
        }

        // For all authenticated states, use the main navigation
        return const MainNavigationWidget();
      },
    );
  }
}
