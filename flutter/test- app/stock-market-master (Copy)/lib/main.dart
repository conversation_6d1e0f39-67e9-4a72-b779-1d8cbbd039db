import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stock_market/Bloc/bloc.dart';
import 'package:stock_market/Bloc/events.dart';
import 'package:stock_market/Bloc/observer.dart';
import 'package:stock_market/Bloc/states.dart';
import 'package:stock_market/class/action.dart';
import 'package:stock_market/db/db_shared.dart';
import 'package:stock_market/db/db_sql.dart';
import 'package:stock_market/pages/auth/login.dart';
import 'package:stock_market/pages/auth/register.dart';
import 'package:stock_market/pages/loading.dart';
import 'package:stock_market/pages/market/details_actions_widget.dart';
import 'package:stock_market/pages/market/market_widget.dart';
import 'package:stock_market/pages/search_bar.dart';
import 'package:stock_market/pages/wallet/wallet_widget.dart';

Future<void> main() async {
  WidgetsFlutterBinding
      .ensureInitialized(); // Ensure initialization before runApp
  await DatabaseHelper.instance.initDatabase(); // Initialize database
  Bloc.observer = MyBlocObserver();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => MarketBloc(), // Provide the bloc here
      child: MaterialApp(
        title: 'StockMarket',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          visualDensity: VisualDensity.adaptivePlatformDensity,
        ),
        home: const PagesSwitcher(),
      ),
    );
  }
}

class PagesSwitcher extends StatelessWidget {
  const PagesSwitcher({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MarketBloc, StockMarketStates>(
      builder: (context, state) {
        Widget page;

        if (state is TransitionState) {
          page = const Center(child: LoadingWidget());
        } else if (state is LoginState) {
          page = const LoginPage();
        } else if (state is RegisterState) {
          page = const RegisterPage();
        } else if (state is MarketState) {
          page = const MarketPlace();
        } else if (state is ActionDetailState) {
          ActionStock? action = state.action;
          //Details of the action
          page = ActionDetailsWidget(action: action);
        } else if (state is WalletState) {
          page = WalletWidget();
        } else if (state is SearchState) {
          page = SearchBarMarket();
        } else {
          page = const Center(child: CircularProgressIndicator());
        }

        return Scaffold(
          appBar: (state is MarketState ||
                  state is ActionDetailState ||
                  state is SearchState ||
                  state is WalletState)
              ? appBarMarket(context, state)
              : null,
          body: AnimatedSwitcher(
            duration: const Duration(milliseconds: 500), // Animation duration
            switchInCurve: Curves.easeOutQuart,
            switchOutCurve: Curves.linear,
            transitionBuilder: (Widget child, Animation<double> animation) {
              return FadeTransition(
                opacity: animation,
                child: ScaleTransition(
                  scale: Tween<double>(begin: 0.95, end: 1).animate(animation),
                  child: child,
                ),
              );
            },
            child: page,
          ),
        );
      },
    );
  }
}

AppBar appBarMarket(BuildContext context, StockMarketStates state) {
  return AppBar(
    toolbarHeight: 80, // Increase AppBar height to 80 pixels
    title: GestureDetector(
      onTap: () {
        // Handle transition to MarketState if we are in ActionDetailState
        BlocProvider.of<MarketBloc>(context).add(
          ChangeStateWithTransition(
              stateNext: StateApp.market, stateNow: StateApp.market),
        );
      },
      child: const Text("MarketPlace", style: TextStyle(fontSize: 20)),
    ),
    actions: [
      IconButton(
        onPressed: () {
          BlocProvider.of<MarketBloc>(context).add(
            ChangeStateWithTransition(
                stateNext: StateApp.search, stateNow: StateApp.market),
          );
        },
        icon: const Icon(Icons.search),
      ),
      IconButton(
        onPressed: () {
          BlocProvider.of<MarketBloc>(context).add(
            ChangeStateWithTransition(
                stateNext: StateApp.wallet, stateNow: StateApp.market),
          );
        },
        icon: const Icon(Icons.credit_card),
      ),
      IconButton(
        onPressed: () async {
          // Change state to go to login screen
          await removeCookie();
          BlocProvider.of<MarketBloc>(context).add(
            ChangeStateWithTransition(
                stateNext: StateApp.login, stateNow: StateApp.market),
          );
        },
        icon: const Icon(
          Icons.exit_to_app,
          color: Color.fromARGB(255, 219, 119, 119),
        ),
      ),
    ],
  );
}
