/// App dimensions and spacing constants for consistent layout
class AppDimensions {
  // Spacing
  static const double spaceXS = 4.0;
  static const double spaceSM = 8.0;
  static const double spaceMD = 16.0;
  static const double spaceLG = 24.0;
  static const double spaceXL = 32.0;
  static const double spaceXXL = 48.0;
  
  // Padding
  static const double paddingXS = 4.0;
  static const double paddingSM = 8.0;
  static const double paddingMD = 16.0;
  static const double paddingLG = 24.0;
  static const double paddingXL = 32.0;
  
  // Margins
  static const double marginXS = 4.0;
  static const double marginSM = 8.0;
  static const double marginMD = 16.0;
  static const double marginLG = 24.0;
  static const double marginXL = 32.0;
  
  // Border radius
  static const double radiusXS = 4.0;
  static const double radiusSM = 8.0;
  static const double radiusMD = 12.0;
  static const double radiusLG = 16.0;
  static const double radiusXL = 24.0;
  static const double radiusCircular = 50.0;
  
  // Elevation
  static const double elevationNone = 0.0;
  static const double elevationSM = 2.0;
  static const double elevationMD = 4.0;
  static const double elevationLG = 8.0;
  static const double elevationXL = 16.0;
  
  // Icon sizes
  static const double iconXS = 16.0;
  static const double iconSM = 20.0;
  static const double iconMD = 24.0;
  static const double iconLG = 32.0;
  static const double iconXL = 48.0;
  
  // Button dimensions
  static const double buttonHeight = 48.0;
  static const double buttonHeightSM = 36.0;
  static const double buttonHeightLG = 56.0;
  static const double buttonMinWidth = 88.0;
  
  // AppBar
  static const double appBarHeight = 80.0;
  static const double appBarElevation = 4.0;
  
  // Card dimensions
  static const double cardElevation = 4.0;
  static const double cardRadius = 12.0;
  static const double cardPadding = 16.0;
  
  // Input field dimensions
  static const double inputHeight = 56.0;
  static const double inputRadius = 12.0;
  static const double inputPadding = 16.0;
  
  // Stock tile dimensions
  static const double stockTileHeight = 80.0;
  static const double stockTilePadding = 16.0;
  static const double stockTileRadius = 12.0;
  
  // Banner dimensions
  static const double bannerHeight = 40.0;
  static const double bannerRadius = 8.0;
  
  // Animation durations (in milliseconds)
  static const int animationFast = 200;
  static const int animationMedium = 300;
  static const int animationSlow = 500;
}
