import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';

const String baseUrl = 'https://mock-stock-data-server-production.up.railway.app/';

enum DateHistory { oneDay, oneWeek, oneMonth, oneYear, fiveYear, tenYear }

class ActionStock {
  final String name;
  final String currency;
  final DateTime date;
  double rate;
  List<(double, DateTime)> history; // Mutable list now
  final String? user;

  ActionStock({
    required this.name,
    required this.currency,
    required this.date,
    required this.rate,
    this.user,
    List<(double, DateTime)>? history,
  }) : history = history ?? [];

  // Method to create an instance of ActionStock from a JSON
  factory ActionStock.fromJson(String name, Map<String, dynamic> json) {
    try {
      return ActionStock(
        name: name,
        currency: json['currency'],
        date: DateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'")
            .parse(json["datetime"]),
        rate: (json['rate'] is num)
            ? double.parse(json['rate'].toStringAsFixed(2))
            : 0.0, // Handle NaN or null
      );
    } catch (e) {
      print("Error parsing $json: $e");
      return ActionStock(
          name: name, currency: "USD", date: DateTime.now(), rate: 0.0);
    }
  }

  // Method to retrieve the price history of an action
  Future<void> fetchStockHistory(String startDate, String endDate) async {
    final response = await http.get(Uri.parse(
        '$baseUrl/hist/$name?start_date=$startDate&end_date=$endDate'));

    if (response.statusCode == 200) {
      Map<String, dynamic> data = json.decode(response.body);
      List<(double, DateTime)> historyRecover = [];

      for (var item in data['values']) {
        try {
          DateTime parsedDate = DateFormat(
                  "EEE, dd MMM yyyy HH:mm:ss 'GMT'", "en_US")
              .parseUTC(item[
                  'date']) // 🔹 Utilise `parseUTC()` to avoid errors
              .toLocal(); // 🔹 Convert the date to local time

          historyRecover.add((item['close'] as double, parsedDate));
        } catch (e) {
          print("Error parsing date : ${item['date']} - $e");
        }
      }

      history = historyRecover;
    } else {
      throw Exception('Failed to load stock history for $name');
    }
  }

  // Method to retrieve the history for 1 day
  Future<void> historyActionOneDay() async {
    final now = DateTime.now();
    final String startDate =
        DateFormat('yyyy-MM-dd').format(now.subtract(const Duration(days: 1)));
    final String endDate = DateFormat('yyyy-MM-dd').format(now);

    await fetchStockHistory(startDate, endDate);
  }

  // Method to retrieve the history for 1 week
  Future<void> historyActionOneWeek() async {
    final now = DateTime.now();
    final String startDate =
        DateFormat('yyyy-MM-dd').format(now.subtract(const Duration(days: 7)));
    final String endDate = DateFormat('yyyy-MM-dd').format(now);

    await fetchStockHistory(startDate, endDate);
  }

  // Method to retrieve the history for 1 month
  Future<void> historyActionOneMonth() async {
    final now = DateTime.now();
    final String startDate =
        DateFormat('yyyy-MM-dd').format(now.subtract(const Duration(days: 30)));
    final String endDate = DateFormat('yyyy-MM-dd').format(now);

    await fetchStockHistory(startDate, endDate);
  }

  // Method to retrieve the history for 1 year
  Future<void> historyActionOneYear() async {
    final now = DateTime.now();
    final String startDate = DateFormat('yyyy-MM-dd')
        .format(now.subtract(const Duration(days: 365)));
    final String endDate = DateFormat('yyyy-MM-dd').format(now);

    await fetchStockHistory(startDate, endDate);
  }

  // Method to retrieve the history for 5 years
  Future<void> historyActionFiveYear() async {
    final now = DateTime.now();
    final String startDate = DateFormat('yyyy-MM-dd')
        .format(now.subtract(const Duration(days: 365 * 5)));
    final String endDate = DateFormat('yyyy-MM-dd').format(now);

    await fetchStockHistory(startDate, endDate);
  }

  // Method to retrieve the history for 10 years
  Future<void> historyActionTenYear() async {
    final now = DateTime.now();
    final String startDate = DateFormat('yyyy-MM-dd')
        .format(now.subtract(const Duration(days: 365 * 10)));
    final String endDate = DateFormat('yyyy-MM-dd').format(now);

    await fetchStockHistory(startDate, endDate);
  }

  Future<void> updateRate() async {
    ActionStock data = await StockService().fetchStockData(name);

    rate = data.rate;
  }
}

// Service to interact with the API server
class StockService {
  // Method to retrieve the list of available stocks
  Future<List<String>> fetchStocksList() async {
    final response = await http.get(Uri.parse(baseUrl + 'stocks_list'));

    if (response.statusCode == 200) {
      List<dynamic> data = json.decode(response.body);
      return List<String>.from(data);
    } else {
      throw Exception('Failed to load stock symbols');
    }
  }

  // Method to retrieve specific stock information
  Future<ActionStock> fetchStockData(String symbol) async {
    final response = await http.get(Uri.parse(
        'https://mock-stock-data-server-production.up.railway.app/exchange_rate/$symbol'));

    if (response.statusCode == 200) {
      try {
        final String responseBody = response.body;
        final Map<String, dynamic> data = json.decode(responseBody);

        return ActionStock.fromJson(symbol, data);
      } catch (e) {
        print(symbol);
        print("Error parsing JSON : $e");
        throw Exception("Error parsing JSON: ${e.toString()}");
      }
    } else {
      throw Exception("Failed to load stock data");
    }
  }
}

class ActionDB {
  double price;
  String name;
  int count;

  ActionDB({required this.name, required this.count, required this.price});

  Future<ActionStock> toActionStock() async {
    return StockService().fetchStockData(name);
  }
}
