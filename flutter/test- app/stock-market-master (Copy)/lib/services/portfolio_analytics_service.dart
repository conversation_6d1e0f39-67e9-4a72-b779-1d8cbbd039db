import 'dart:math';
import '../class/action.dart';
import '../class/user.dart';
import '../db/db_sql.dart';
import '../db/db_shared.dart';

/// Portfolio analytics service for calculating performance metrics
class PortfolioAnalyticsService {
  static final PortfolioAnalyticsService _instance = PortfolioAnalyticsService._internal();
  factory PortfolioAnalyticsService() => _instance;
  PortfolioAnalyticsService._internal();

  /// Get comprehensive portfolio data
  Future<PortfolioData> getPortfolioData() async {
    try {
      // Load user and holdings
      String username = await loadUserCookie();
      List<User> users = await loadUserData(user: username);
      User? user = users.isNotEmpty ? users.first : null;
      
      if (user == null) {
        throw Exception('User not found');
      }

      List<ActionDB> holdings = await loadActionsUser();
      
      // Get current market prices for all holdings
      List<PortfolioHolding> portfolioHoldings = [];
      double totalCurrentValue = 0;
      double totalCostBasis = 0;

      for (ActionDB holding in holdings) {
        try {
          ActionStock currentStock = await StockService().fetchStockData(holding.name);
          
          double currentValue = holding.count * currentStock.rate;
          double costBasis = holding.count * holding.price;
          double gainLoss = currentValue - costBasis;
          double gainLossPercent = costBasis > 0 ? (gainLoss / costBasis) * 100 : 0;

          portfolioHoldings.add(PortfolioHolding(
            symbol: holding.name,
            name: holding.name,
            shares: holding.count,
            avgCostPerShare: holding.price,
            currentPrice: currentStock.rate,
            costBasis: costBasis,
            currentValue: currentValue,
            gainLoss: gainLoss,
            gainLossPercent: gainLossPercent,
            currency: currentStock.currency,
          ));

          totalCurrentValue += currentValue;
          totalCostBasis += costBasis;
        } catch (e) {
          print('Error fetching data for ${holding.name}: $e');
          // Add holding with last known price if API fails
          double currentValue = holding.count * holding.price;
          portfolioHoldings.add(PortfolioHolding(
            symbol: holding.name,
            name: holding.name,
            shares: holding.count,
            avgCostPerShare: holding.price,
            currentPrice: holding.price,
            costBasis: currentValue,
            currentValue: currentValue,
            gainLoss: 0,
            gainLossPercent: 0,
            currency: 'USD',
          ));
          totalCurrentValue += currentValue;
          totalCostBasis += currentValue;
        }
      }

      // Calculate portfolio metrics
      double totalGainLoss = totalCurrentValue - totalCostBasis;
      double totalGainLossPercent = totalCostBasis > 0 ? (totalGainLoss / totalCostBasis) * 100 : 0;
      double totalPortfolioValue = user.wallet + totalCurrentValue;

      // Generate performance history (mock data for now - in real app would come from transaction history)
      List<PortfolioPerformancePoint> performanceHistory = _generatePerformanceHistory(
        totalCurrentValue,
        totalGainLossPercent,
      );

      return PortfolioData(
        user: user,
        holdings: portfolioHoldings,
        totalCurrentValue: totalCurrentValue,
        totalCostBasis: totalCostBasis,
        totalGainLoss: totalGainLoss,
        totalGainLossPercent: totalGainLossPercent,
        totalPortfolioValue: totalPortfolioValue,
        cashBalance: user.wallet,
        performanceHistory: performanceHistory,
      );
    } catch (e) {
      throw Exception('Failed to load portfolio data: $e');
    }
  }

  /// Generate mock performance history (in real app, this would come from historical data)
  List<PortfolioPerformancePoint> _generatePerformanceHistory(double currentValue, double gainLossPercent) {
    List<PortfolioPerformancePoint> history = [];
    DateTime now = DateTime.now();
    
    // Generate 30 days of mock data
    for (int i = 29; i >= 0; i--) {
      DateTime date = now.subtract(Duration(days: i));
      
      // Create realistic fluctuation around the current value
      double variation = (Random().nextDouble() - 0.5) * 0.1; // ±5% variation
      double dayValue = currentValue * (1 + variation);
      
      // Trend towards current performance over time
      double trendFactor = (30 - i) / 30;
      double adjustedValue = dayValue * (1 + (gainLossPercent / 100) * trendFactor * 0.1);
      
      history.add(PortfolioPerformancePoint(
        date: date,
        value: adjustedValue,
      ));
    }
    
    return history;
  }

  /// Get top performers (best and worst)
  List<PortfolioHolding> getTopPerformers(List<PortfolioHolding> holdings, {bool best = true}) {
    List<PortfolioHolding> sorted = List.from(holdings);
    sorted.sort((a, b) => best 
        ? b.gainLossPercent.compareTo(a.gainLossPercent)
        : a.gainLossPercent.compareTo(b.gainLossPercent));
    return sorted.take(3).toList();
  }

  /// Calculate portfolio allocation percentages
  List<AllocationData> getPortfolioAllocation(List<PortfolioHolding> holdings) {
    double totalValue = holdings.fold(0, (sum, holding) => sum + holding.currentValue);
    
    return holdings.map((holding) => AllocationData(
      symbol: holding.symbol,
      name: holding.name,
      value: holding.currentValue,
      percentage: totalValue > 0 ? (holding.currentValue / totalValue) * 100 : 0,
    )).toList();
  }
}

/// Portfolio data model
class PortfolioData {
  final User user;
  final List<PortfolioHolding> holdings;
  final double totalCurrentValue;
  final double totalCostBasis;
  final double totalGainLoss;
  final double totalGainLossPercent;
  final double totalPortfolioValue;
  final double cashBalance;
  final List<PortfolioPerformancePoint> performanceHistory;

  PortfolioData({
    required this.user,
    required this.holdings,
    required this.totalCurrentValue,
    required this.totalCostBasis,
    required this.totalGainLoss,
    required this.totalGainLossPercent,
    required this.totalPortfolioValue,
    required this.cashBalance,
    required this.performanceHistory,
  });
}

/// Individual holding data
class PortfolioHolding {
  final String symbol;
  final String name;
  final int shares;
  final double avgCostPerShare;
  final double currentPrice;
  final double costBasis;
  final double currentValue;
  final double gainLoss;
  final double gainLossPercent;
  final String currency;

  PortfolioHolding({
    required this.symbol,
    required this.name,
    required this.shares,
    required this.avgCostPerShare,
    required this.currentPrice,
    required this.costBasis,
    required this.currentValue,
    required this.gainLoss,
    required this.gainLossPercent,
    required this.currency,
  });
}

/// Performance point for charts
class PortfolioPerformancePoint {
  final DateTime date;
  final double value;

  PortfolioPerformancePoint({
    required this.date,
    required this.value,
  });
}

/// Allocation data for pie charts
class AllocationData {
  final String symbol;
  final String name;
  final double value;
  final double percentage;

  AllocationData({
    required this.symbol,
    required this.name,
    required this.value,
    required this.percentage,
  });
}
