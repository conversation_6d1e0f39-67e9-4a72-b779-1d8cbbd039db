import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:stock_market/class/action.dart';
import 'package:stock_market/class/user.dart';
import 'package:stock_market/db/db_shared.dart';

Future<List<User>> loadUserData({String? user}) async {
  final db = await DatabaseHelper.instance.database;

  List<Map<String, dynamic>> maps;

  if (user != null) {
    maps = await db.query(
      'users',
      where: 'username = ?',
      whereArgs: [user],
    );

    if (maps.isEmpty) {
      return [];
    }

    return [
      User(
        username: maps.first['username'],
        password: maps.first['password'],
        wallet: maps.first['wallet'],
      )
    ];
  } else {
    // Request for all users
    maps = await db.query('users');

    return List.generate(maps.length, (i) {
      return User(
        username: maps[i]['username'],
        password: maps[i]['password'],
        wallet: maps[i]['wallet'],
      );
    });
  }
}

Future<int> getUserIdFromCookie() async {
  final user =
      await loadUserCookie(); // Get user from cookie or storage
  final db = await DatabaseHelper.instance.database;

  final List<Map<String, dynamic>> userResult = await db.query(
    'users',
    columns: ['id'],
    where: 'username = ?',
    whereArgs: [user],
  );

  if (userResult.isEmpty) {
    throw Exception("Utilisateur non trouvé.");
  }

  return userResult.first['id'];
}

Future<List<ActionDB>> loadActionUser(String actionName) async {
  final db = await DatabaseHelper.instance.database;
  int userID = await getUserIdFromCookie();

  final List<Map<String, dynamic>> maps = await db.query(
    'actions',
    columns: ['name', 'count', 'priceBuy'],
    where: 'user = ? AND name = ?',
    whereArgs: [userID, actionName],
  );

  // Return an empty list if no result is found
  if (maps.isEmpty) {
    return [];
  }

  // Convert each element of `maps` to an `ActionDB` object
  List<ActionDB> actions = maps
      .map((map) => ActionDB(
            name: map['name'],
            count: map['count'],
            price: map['priceBuy'],
          ))
      .toList();

  return actions; // Return a list of `ActionDB`
}

Future<List<ActionDB>> loadActionsUser() async {
  final db = await DatabaseHelper.instance.database;

  int userID = await getUserIdFromCookie();

  // Get actions linked to this user
  final List<Map<String, dynamic>> maps = await db.query(
    'actions',
    columns: ['name', 'count', 'priceBuy'],
    where: 'user = ?',
    whereArgs: [userID],
  );

  // Convert the data into a list of ActionDB objects
  List<ActionDB> actions = maps.map((map) {
    return ActionDB(
      name: map['name'],
      count: map['count'],
      price: map['priceBuy'],
    );
  }).toList();

  return actions;
}

Future<void> saveUser(User user) async {
  final db = await DatabaseHelper.instance.database;
  await db.insert(
    'users',
    {
      'username': user.username,
      'password': user.password,
      'wallet': user.wallet
    },
    conflictAlgorithm: ConflictAlgorithm.replace,
  );
}

Future<void> saveAction({required ActionDB action}) async {
  final db = await DatabaseHelper.instance.database;
  int userID = await getUserIdFromCookie();

  // Check if the user already has this action
  List<Map<String, dynamic>> existingActions = await db.query(
    'actions',
    where: 'user = ? AND name = ?',
    whereArgs: [userID, action.name],
  );

  if (existingActions.isNotEmpty) {
    // Get the total of existing actions and the current average price
    int totalCount = 0;
    double totalValue = 0;

    for (var element in existingActions) {
      int count = element['count'] as int;
      double price = element['priceBuy'] as double;

      totalCount += count;
      totalValue += count * price;
    }

    // Add the new quantity purchased
    totalCount += action.count;
    totalValue += action.count * action.price;

    // Calculate the new average price
    double newAvgPrice = totalValue / totalCount;

    // Update the database with the new average price and total quantity
    await db.update(
      'actions',
      {
        'count': totalCount,
        'priceBuy': newAvgPrice, // Update with the weighted average
      },
      where: 'user = ? AND name = ?',
      whereArgs: [userID, action.name],
    );
  } else {
    // If the action doesn't exist yet, insert a new entry
    await db.insert(
      'actions',
      {
        'user': userID,
        'name': action.name,
        'count': action.count,
        'priceBuy': action.price,
      },
    );
  }
}

// Future<ActionStock> load

class DatabaseHelper {
  static final DatabaseHelper instance = DatabaseHelper._privateConstructor();
  static Database? _database;

  DatabaseHelper._privateConstructor();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await initDatabase();
    return _database!;
  }

  Future<Database> initDatabase() async {
    String path = join(await getDatabasesPath(), 'stock_market.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: (db, version) async {
        // Create the 'users' table
        await db.execute('''
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT UNIQUE NOT NULL,
          password TEXT NOT NULL,
          wallet REAL NOT NULL
        );

      ''');

        // Create the 'actions' table with a foreign key that references 'id' in 'users'
        await db.execute('''
        CREATE TABLE IF NOT EXISTS actions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user INTEGER NOT NULL,
          name TEXT UNIQUE NOT NULL,
          count INTEGER NOT NULL,
          priceBuy REAL NOT NULL,
          FOREIGN KEY (user) REFERENCES users(id) ON DELETE CASCADE
        );
      ''');
      },
    );
  }
}

Future<void> manageWalletUser(
    {required bool plus, required double price}) async {
  final db = await DatabaseHelper.instance.database;
  final user = await loadUserCookie();

  // Get the user ID and current wallet balance
  final List<Map<String, dynamic>> userResult = await db.query(
    'users',
    columns: [
      'id',
      'wallet'
    ], // Assume "wallet" is the field representing the user's money
    where: 'username = ?',
    whereArgs: [user],
  );

  if (userResult.isEmpty) {
    print("Utilisateur non trouvé.");
    return;
  }

  int userID = userResult.first['id'];
  double walletBalance = userResult.first['wallet'];

  // If 'plus' is true, it means we want to add money (earn money)
  if (plus) {
    walletBalance += price;
  } else {
    // If 'plus' is false, it means we want to withdraw money (pay or lose money)
    if (walletBalance >= price) {
      walletBalance -= price;
    } else {
      print("Fonds insuffisants dans le portefeuille.");
      return;
    }
  }

  // Update the user's wallet balance in the database
  await db.update(
    'users',
    {'wallet': walletBalance},
    where: 'id = ?',
    whereArgs: [userID],
  );
}

Future<void> deleteAction({required ActionDB action}) async {
  final db = await DatabaseHelper.instance.database;

  final List<Map<String, dynamic>> result = await db.query(
    'actions',
    columns: ['id', 'count'],
    where: 'name = ? AND user = ?',
    whereArgs: [action.name, await getUserIdFromCookie()],
  );

  if (result.isEmpty) {
    return;
  }

  int existingCount = result.first['count'];

  if (existingCount > action.count) {
    await db.update(
      'actions',
      {'count': existingCount - action.count}, // Reduce the count
      where: 'name = ? AND user = ?',
      whereArgs: [action.name, await getUserIdFromCookie()],
    );
  } else {
    await db.delete(
      'actions',
      where: 'name = ? AND user = ?',
      whereArgs: [action.name, await getUserIdFromCookie()],
    );
  }
}
