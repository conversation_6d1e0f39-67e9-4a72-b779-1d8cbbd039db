// ✅ Function to retrieve the username stored
import 'package:shared_preferences/shared_preferences.dart';

Future<String> loadUserCookie() async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  return prefs.getString("user_username") ?? "";
}

Future<void> saveCookieUser(String username) async {
  String user = await loadUserCookie();

  if (user.isEmpty) {
    // Store the username in SharedPreferences
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString("user_username", username);
  }
}

Future<void> removeCookie() async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  await prefs.remove("user_username"); // Remove the "user_username" key
}
