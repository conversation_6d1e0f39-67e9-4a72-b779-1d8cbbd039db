import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../Bloc/bloc.dart';
import '../../Bloc/events.dart';
import '../../class/user.dart';
import '../../db/db_shared.dart';
import '../../db/db_sql.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_dimensions.dart';
import '../../theme/app_text_styles.dart';
import '../../widgets/styled_components.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  User? _currentUser;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    try {
      String username = await loadUserCookie();
      if (username.isNotEmpty) {
        List<User> users = await loadUserData(user: username);
        if (users.isNotEmpty) {
          setState(() {
            _currentUser = users.first;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_currentUser == null) {
      return const Center(
        child: Text('Unable to load user data'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingMD),
      child: Column(
        children: [
          // Profile Header
          _buildProfileHeader(),
          const SizedBox(height: AppDimensions.spaceLG),
          
          // Account Information
          _buildAccountInfo(),
          const SizedBox(height: AppDimensions.spaceLG),
          
          // Settings Options
          _buildSettingsOptions(),
          const SizedBox(height: AppDimensions.spaceLG),
          
          // Logout Button
          _buildLogoutButton(),
        ],
      ),
    );
  }

  Widget _buildProfileHeader() {
    return StyledCard(
      child: Column(
        children: [
          // Avatar
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: AppColors.primaryGradient,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(AppDimensions.radiusCircular),
            ),
            child: Center(
              child: Text(
                _currentUser!.username.substring(0, 1).toUpperCase(),
                style: AppTextStyles.displaySmall.copyWith(
                  color: AppColors.textOnPrimary,
                ),
              ),
            ),
          ),
          const SizedBox(height: AppDimensions.spaceMD),
          
          // Username
          Text(
            _currentUser!.username,
            style: AppTextStyles.headlineMedium,
          ),
          const SizedBox(height: AppDimensions.spaceXS),
          
          // Member since
          Text(
            'Member since 2024',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountInfo() {
    return StyledCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Account Information',
            style: AppTextStyles.headlineSmall,
          ),
          const SizedBox(height: AppDimensions.spaceMD),
          
          _buildInfoRow(
            icon: Icons.person_outline,
            label: 'Username',
            value: _currentUser!.username,
          ),
          const SizedBox(height: AppDimensions.spaceMD),
          
          _buildInfoRow(
            icon: Icons.account_balance_wallet_outlined,
            label: 'Current Balance',
            value: '\$${_currentUser!.wallet.toStringAsFixed(2)}',
            valueColor: AppColors.success,
          ),
          const SizedBox(height: AppDimensions.spaceMD),
          
          _buildInfoRow(
            icon: Icons.trending_up,
            label: 'Account Type',
            value: 'Standard Trader',
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: AppColors.primary,
          size: AppDimensions.iconMD,
        ),
        const SizedBox(width: AppDimensions.spaceMD),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              Text(
                value,
                style: AppTextStyles.bodyLarge.copyWith(
                  color: valueColor ?? AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsOptions() {
    return StyledCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Settings',
            style: AppTextStyles.headlineSmall,
          ),
          const SizedBox(height: AppDimensions.spaceMD),
          
          _buildSettingOption(
            icon: Icons.notifications_outlined,
            title: 'Notifications',
            subtitle: 'Manage your notification preferences',
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Notifications settings coming soon!')),
              );
            },
          ),
          
          _buildSettingOption(
            icon: Icons.security_outlined,
            title: 'Security',
            subtitle: 'Password and security settings',
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Security settings coming soon!')),
              );
            },
          ),
          
          _buildSettingOption(
            icon: Icons.help_outline,
            title: 'Help & Support',
            subtitle: 'Get help and contact support',
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Help & Support coming soon!')),
              );
            },
          ),
          
          _buildSettingOption(
            icon: Icons.info_outline,
            title: 'About',
            subtitle: 'App version and information',
            onTap: () {
              _showAboutDialog();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSettingOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppDimensions.radiusSM),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingSM),
        child: Row(
          children: [
            Icon(
              icon,
              color: AppColors.primary,
              size: AppDimensions.iconMD,
            ),
            const SizedBox(width: AppDimensions.spaceMD),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.titleMedium,
                  ),
                  Text(
                    subtitle,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: AppColors.textHint,
              size: AppDimensions.iconSM,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoutButton() {
    return StyledGradientButton(
      text: 'Logout',
      width: double.infinity,
      gradientColors: [AppColors.error, AppColors.error.withValues(alpha: 0.8)],
      icon: Icons.exit_to_app,
      onPressed: () => _showLogoutDialog(),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusLG),
          ),
          title: Text(
            'Logout',
            style: AppTextStyles.headlineSmall,
          ),
          content: Text(
            'Are you sure you want to logout?',
            style: AppTextStyles.bodyMedium,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Cancel',
                style: AppTextStyles.labelLarge.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            StyledGradientButton(
              text: 'Logout',
              gradientColors: [AppColors.error, AppColors.error.withValues(alpha: 0.8)],
              onPressed: () async {
                Navigator.pop(context);
                await removeCookie();
                if (context.mounted) {
                  BlocProvider.of<MarketBloc>(context).add(
                    ChangeStateWithTransition(
                      stateNext: StateApp.login,
                      stateNow: StateApp.profile,
                    ),
                  );
                }
              },
            ),
          ],
        );
      },
    );
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusLG),
          ),
          title: Text(
            'About StockMarket',
            style: AppTextStyles.headlineSmall,
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Version 1.0.0',
                style: AppTextStyles.bodyMedium,
              ),
              const SizedBox(height: AppDimensions.spaceSM),
              Text(
                'A modern stock trading app built with Flutter.',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Close',
                style: AppTextStyles.labelLarge.copyWith(
                  color: AppColors.primary,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
