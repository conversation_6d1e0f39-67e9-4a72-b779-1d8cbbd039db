import 'package:flutter/material.dart';
import 'package:stock_market/class/action.dart';
import 'package:stock_market/db/db_sql.dart';
import 'package:stock_market/show_message.dart';

class ButtonSell extends StatelessWidget {
  final ActionStock action;
  final Function updateAction;
  final List<ActionDB?> actionPossess;

  const ButtonSell(
      {required this.action,
      required this.updateAction,
      required this.actionPossess,
      super.key});

  // Function to sell actions
  Future<void> sellActions(int quantitySell, BuildContext context) async {
    int quantityDispo =
        actionPossess.fold<int>(0, (prev, e) => prev + e!.count);

    if (quantitySell > quantityDispo) {
      if (context.mounted) {
        showSnackBar(
          "You can't sell more than you have!",
          Colors.red,
          context,
        );
      }
      return;
    }

    double price = quantitySell * action.rate;

    try {
      ActionDB actionToDelete = ActionDB(
        name: action.name,
        count: quantitySell,
        price: action.rate,
      );

      await deleteAction(action: actionToDelete);

      await manageWalletUser(plus: true, price: price);

      if (context.mounted) {
        showSnackBar(
          "$quantitySell ${action.name} action(s) sell: ${price.toStringAsFixed(2)}\$. Congratulations!",
          Colors.green,
          context,
        );
      }
      updateAction();
    } catch (e) {
      if (context.mounted) {
        showSnackBar(
          "Error: Sell failed!",
          Colors.red,
          context,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () => _showBuyDialog(context),
      style: ElevatedButton.styleFrom(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        backgroundColor: Colors.redAccent,
        elevation: 4,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
      child: const Text(
        "Sell",
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.white, // White text for contrast
        ),
      ),
    );
  }

  void _showBuyDialog(BuildContext context) {
    TextEditingController quantityController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: const Text(
            "Sell actions",
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text("Enter numbers actions :"),
              const SizedBox(height: 10),
              TextField(
                controller: quantityController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: "Ex: 10",
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text("Cancel", style: TextStyle(color: Colors.red)),
            ),
            ElevatedButton(
              onPressed: () async {
                int quantity = int.tryParse(quantityController.text) ?? 0;
                if (quantity > 0) {
                  await sellActions(quantity, context);

                  Navigator.pop(context); // Close the dialog after purchase
                } else {
                  // If the quantity is invalid, show an error message
                  showSnackBar(
                    "Please enter a valid number of actions!",
                    Colors.red,
                    context,
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.lightGreen,
              ),
              child: const Text(
                "Confirm",
                selectionColor: Colors.white,
              ),
            ),
          ],
        );
      },
    );
  }
}
