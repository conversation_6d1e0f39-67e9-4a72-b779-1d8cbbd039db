import 'package:flutter/material.dart';
import '../../class/action.dart';
import '../../db/db_sql.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_dimensions.dart';
import '../../theme/app_text_styles.dart';
import '../../widgets/styled_components.dart';

class TransactionHistory extends StatefulWidget {
  const TransactionHistory({super.key});

  @override
  State<TransactionHistory> createState() => _TransactionHistoryState();
}

class _TransactionHistoryState extends State<TransactionHistory> {
  late Future<List<ActionDB>> _transactionsFuture;

  @override
  void initState() {
    super.initState();
    _transactionsFuture = _loadTransactions();
  }

  Future<List<ActionDB>> _loadTransactions() async {
    try {
      return await loadActionsUser();
    } catch (e) {
      return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Transaction History', style: AppTextStyles.appBarTitle),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
      ),
      body: FutureBuilder<List<ActionDB>>(
        future: _transactionsFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: AppDimensions.iconXL,
                    color: AppColors.error,
                  ),
                  const SizedBox(height: AppDimensions.spaceMD),
                  Text(
                    'Failed to load transactions',
                    style: AppTextStyles.titleLarge,
                  ),
                ],
              ),
            );
          }

          final transactions = snapshot.data ?? [];

          if (transactions.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.receipt_long_outlined,
                    size: AppDimensions.iconXL,
                    color: AppColors.textHint,
                  ),
                  const SizedBox(height: AppDimensions.spaceMD),
                  Text(
                    'No Transactions Yet',
                    style: AppTextStyles.titleLarge.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: AppDimensions.spaceXS),
                  Text(
                    'Your transaction history will appear here',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textHint,
                    ),
                  ),
                ],
              ),
            );
          }

          return _buildTransactionsList(transactions);
        },
      ),
    );
  }

  Widget _buildTransactionsList(List<ActionDB> transactions) {
    return RefreshIndicator(
      onRefresh: () async {
        setState(() {
          _transactionsFuture = _loadTransactions();
        });
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(AppDimensions.paddingMD),
        itemCount: transactions.length + 1, // +1 for summary card
        itemBuilder: (context, index) {
          if (index == 0) {
            return _buildSummaryCard(transactions);
          }
          
          final transaction = transactions[index - 1];
          return _buildTransactionTile(transaction);
        },
      ),
    );
  }

  Widget _buildSummaryCard(List<ActionDB> transactions) {
    double totalInvested = transactions.fold(
      0, 
      (sum, transaction) => sum + (transaction.count * transaction.price),
    );
    
    int totalShares = transactions.fold(
      0, 
      (sum, transaction) => sum + transaction.count,
    );

    return StyledCard(
      margin: const EdgeInsets.only(bottom: AppDimensions.marginLG),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Transaction Summary',
            style: AppTextStyles.headlineSmall,
          ),
          const SizedBox(height: AppDimensions.spaceMD),
          
          Row(
            children: [
              Expanded(
                child: _buildSummaryMetric(
                  label: 'Total Invested',
                  value: '\$${totalInvested.toStringAsFixed(2)}',
                  icon: Icons.account_balance_wallet_outlined,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(width: AppDimensions.spaceMD),
              Expanded(
                child: _buildSummaryMetric(
                  label: 'Total Shares',
                  value: totalShares.toString(),
                  icon: Icons.pie_chart_outline,
                  color: AppColors.secondary,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.spaceMD),
          
          Row(
            children: [
              Expanded(
                child: _buildSummaryMetric(
                  label: 'Unique Stocks',
                  value: transactions.map((t) => t.name).toSet().length.toString(),
                  icon: Icons.diversity_3_outlined,
                  color: AppColors.accent,
                ),
              ),
              const SizedBox(width: AppDimensions.spaceMD),
              Expanded(
                child: _buildSummaryMetric(
                  label: 'Avg. Price',
                  value: totalShares > 0 
                      ? '\$${(totalInvested / totalShares).toStringAsFixed(2)}'
                      : '\$0.00',
                  icon: Icons.trending_up_outlined,
                  color: AppColors.warning,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryMetric({
    required String label,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingMD),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusSM),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: AppDimensions.iconSM),
              const SizedBox(width: AppDimensions.spaceXS),
              Text(
                label,
                style: AppTextStyles.labelMedium.copyWith(color: color),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spaceXS),
          Text(
            value,
            style: AppTextStyles.titleMedium.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionTile(ActionDB transaction) {
    final totalValue = transaction.count * transaction.price;
    
    return StyledCard(
      margin: const EdgeInsets.only(bottom: AppDimensions.marginSM),
      child: Row(
        children: [
          // Stock symbol avatar
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: AppColors.primaryGradient,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(AppDimensions.radiusMD),
            ),
            child: Center(
              child: Text(
                transaction.name.substring(0, 2).toUpperCase(),
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.textOnPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: AppDimensions.spaceMD),
          
          // Transaction details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      transaction.name,
                      style: AppTextStyles.titleMedium,
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingSM,
                        vertical: AppDimensions.paddingXS,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.success.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppDimensions.radiusXS),
                        border: Border.all(color: AppColors.success.withValues(alpha: 0.3)),
                      ),
                      child: Text(
                        'BUY',
                        style: AppTextStyles.labelSmall.copyWith(
                          color: AppColors.success,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.spaceXS),
                
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${transaction.count} shares @ \$${transaction.price.toStringAsFixed(2)}',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      '\$${totalValue.toStringAsFixed(2)}',
                      style: AppTextStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
