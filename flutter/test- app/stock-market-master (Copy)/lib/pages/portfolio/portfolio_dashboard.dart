import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../services/portfolio_analytics_service.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_dimensions.dart';
import '../../theme/app_text_styles.dart';
import '../../widgets/styled_components.dart';
import 'transaction_history.dart';

class PortfolioDashboard extends StatefulWidget {
  const PortfolioDashboard({super.key});

  @override
  State<PortfolioDashboard> createState() => _PortfolioDashboardState();
}

class _PortfolioDashboardState extends State<PortfolioDashboard> {
  final PortfolioAnalyticsService _analyticsService = PortfolioAnalyticsService();
  late Future<PortfolioData> _portfolioDataFuture;

  @override
  void initState() {
    super.initState();
    _portfolioDataFuture = _analyticsService.getPortfolioData();
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        setState(() {
          _portfolioDataFuture = _analyticsService.getPortfolioData();
        });
      },
      child: FutureBuilder<PortfolioData>(
        future: _portfolioDataFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: AppDimensions.iconXL,
                    color: AppColors.error,
                  ),
                  const SizedBox(height: AppDimensions.spaceMD),
                  Text(
                    'Failed to load portfolio data',
                    style: AppTextStyles.titleLarge,
                  ),
                  const SizedBox(height: AppDimensions.spaceSM),
                  StyledGradientButton(
                    text: 'Retry',
                    onPressed: () {
                      setState(() {
                        _portfolioDataFuture = _analyticsService.getPortfolioData();
                      });
                    },
                  ),
                ],
              ),
            );
          }

          final portfolioData = snapshot.data!;
          return Scaffold(
            body: _buildPortfolioDashboard(portfolioData),
            floatingActionButton: FloatingActionButton.extended(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const TransactionHistory(),
                  ),
                );
              },
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.textOnPrimary,
              icon: const Icon(Icons.history),
              label: const Text('History'),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPortfolioDashboard(PortfolioData data) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingMD),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Portfolio Overview Cards
          _buildOverviewCards(data),
          const SizedBox(height: AppDimensions.spaceLG),

          // Performance Chart
          _buildPerformanceChart(data),
          const SizedBox(height: AppDimensions.spaceLG),

          // Holdings Allocation
          if (data.holdings.isNotEmpty) ...[
            _buildAllocationChart(data),
            const SizedBox(height: AppDimensions.spaceLG),
          ],

          // Top Performers
          if (data.holdings.isNotEmpty) ...[
            _buildTopPerformers(data),
            const SizedBox(height: AppDimensions.spaceLG),
          ],

          // Holdings List
          _buildHoldingsList(data),
        ],
      ),
    );
  }

  Widget _buildOverviewCards(PortfolioData data) {
    return Row(
      children: [
        Expanded(
          child: _buildMetricCard(
            title: 'Total Value',
            value: '\$${data.totalPortfolioValue.toStringAsFixed(2)}',
            subtitle: 'Portfolio + Cash',
            icon: Icons.account_balance_wallet,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(width: AppDimensions.spaceMD),
        Expanded(
          child: _buildMetricCard(
            title: 'Today\'s P&L',
            value: '${data.totalGainLoss >= 0 ? '+' : ''}\$${data.totalGainLoss.toStringAsFixed(2)}',
            subtitle: '${data.totalGainLossPercent >= 0 ? '+' : ''}${data.totalGainLossPercent.toStringAsFixed(2)}%',
            icon: data.totalGainLoss >= 0 ? Icons.trending_up : Icons.trending_down,
            color: data.totalGainLoss >= 0 ? AppColors.success : AppColors.error,
          ),
        ),
      ],
    );
  }

  Widget _buildMetricCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    return StyledCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: AppDimensions.iconMD),
              const SizedBox(width: AppDimensions.spaceSM),
              Text(title, style: AppTextStyles.labelLarge),
            ],
          ),
          const SizedBox(height: AppDimensions.spaceSM),
          Text(
            value,
            style: AppTextStyles.headlineSmall.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            subtitle,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceChart(PortfolioData data) {
    return StyledCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          StyledSectionHeader(
            title: 'Portfolio Performance',
            subtitle: 'Last 30 days',
          ),
          const SizedBox(height: AppDimensions.spaceLG),
          SizedBox(
            height: 200,
            child: LineChart(
              LineChartData(
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: false,
                  horizontalInterval: 1,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: AppColors.border,
                      strokeWidth: 1,
                    );
                  },
                ),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      interval: 7,
                      getTitlesWidget: (double value, TitleMeta meta) {
                        if (value.toInt() < data.performanceHistory.length) {
                          final date = data.performanceHistory[value.toInt()].date;
                          return Text(
                            '${date.month}/${date.day}',
                            style: AppTextStyles.labelSmall,
                          );
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      interval: 1000,
                      reservedSize: 60,
                      getTitlesWidget: (double value, TitleMeta meta) {
                        return Text(
                          '\$${(value / 1000).toStringAsFixed(0)}K',
                          style: AppTextStyles.labelSmall,
                        );
                      },
                    ),
                  ),
                ),
                borderData: FlBorderData(show: false),
                minX: 0,
                maxX: data.performanceHistory.length.toDouble() - 1,
                minY: data.performanceHistory.map((e) => e.value).reduce((a, b) => a < b ? a : b) * 0.95,
                maxY: data.performanceHistory.map((e) => e.value).reduce((a, b) => a > b ? a : b) * 1.05,
                lineBarsData: [
                  LineChartBarData(
                    spots: data.performanceHistory.asMap().entries.map((entry) {
                      return FlSpot(entry.key.toDouble(), entry.value.value);
                    }).toList(),
                    isCurved: true,
                    gradient: LinearGradient(
                      colors: data.totalGainLoss >= 0 
                          ? [AppColors.success, AppColors.success.withValues(alpha: 0.3)]
                          : [AppColors.error, AppColors.error.withValues(alpha: 0.3)],
                    ),
                    barWidth: 3,
                    isStrokeCapRound: true,
                    dotData: FlDotData(show: false),
                    belowBarData: BarAreaData(
                      show: true,
                      gradient: LinearGradient(
                        colors: data.totalGainLoss >= 0 
                            ? [AppColors.success.withValues(alpha: 0.3), AppColors.success.withValues(alpha: 0.1)]
                            : [AppColors.error.withValues(alpha: 0.3), AppColors.error.withValues(alpha: 0.1)],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAllocationChart(PortfolioData data) {
    final allocations = _analyticsService.getPortfolioAllocation(data.holdings);
    
    return StyledCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          StyledSectionHeader(
            title: 'Portfolio Allocation',
            subtitle: 'Holdings breakdown',
          ),
          const SizedBox(height: AppDimensions.spaceLG),
          SizedBox(
            height: 200,
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: PieChart(
                    PieChartData(
                      sectionsSpace: 2,
                      centerSpaceRadius: 40,
                      sections: allocations.asMap().entries.map((entry) {
                        final index = entry.key;
                        final allocation = entry.value;
                        final color = _getColorForIndex(index);
                        
                        return PieChartSectionData(
                          color: color,
                          value: allocation.percentage,
                          title: '${allocation.percentage.toStringAsFixed(1)}%',
                          radius: 60,
                          titleStyle: AppTextStyles.labelSmall.copyWith(
                            color: AppColors.textOnPrimary,
                            fontWeight: FontWeight.bold,
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: allocations.asMap().entries.map((entry) {
                      final index = entry.key;
                      final allocation = entry.value;
                      final color = _getColorForIndex(index);
                      
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: Row(
                          children: [
                            Container(
                              width: 12,
                              height: 12,
                              decoration: BoxDecoration(
                                color: color,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: AppDimensions.spaceXS),
                            Expanded(
                              child: Text(
                                allocation.symbol,
                                style: AppTextStyles.labelSmall,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getColorForIndex(int index) {
    final colors = [
      AppColors.primary,
      AppColors.secondary,
      AppColors.accent,
      AppColors.warning,
      AppColors.info,
    ];
    return colors[index % colors.length];
  }

  Widget _buildTopPerformers(PortfolioData data) {
    final topGainers = _analyticsService.getTopPerformers(data.holdings, best: true);
    final topLosers = _analyticsService.getTopPerformers(data.holdings, best: false);

    return Row(
      children: [
        if (topGainers.isNotEmpty)
          Expanded(
            child: _buildPerformerCard(
              title: 'Top Gainers',
              holdings: topGainers,
              isGainer: true,
            ),
          ),
        if (topGainers.isNotEmpty && topLosers.isNotEmpty)
          const SizedBox(width: AppDimensions.spaceMD),
        if (topLosers.isNotEmpty)
          Expanded(
            child: _buildPerformerCard(
              title: 'Top Losers',
              holdings: topLosers,
              isGainer: false,
            ),
          ),
      ],
    );
  }

  Widget _buildPerformerCard({
    required String title,
    required List<PortfolioHolding> holdings,
    required bool isGainer,
  }) {
    return StyledCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style: AppTextStyles.titleMedium),
          const SizedBox(height: AppDimensions.spaceMD),
          ...holdings.take(3).map((holding) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(holding.symbol, style: AppTextStyles.bodyMedium),
                Text(
                  '${holding.gainLossPercent >= 0 ? '+' : ''}${holding.gainLossPercent.toStringAsFixed(2)}%',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: holding.gainLossPercent >= 0 ? AppColors.success : AppColors.error,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildHoldingsList(PortfolioData data) {
    if (data.holdings.isEmpty) {
      return StyledCard(
        child: Column(
          children: [
            Icon(
              Icons.account_balance_wallet_outlined,
              size: AppDimensions.iconXL,
              color: AppColors.textHint,
            ),
            const SizedBox(height: AppDimensions.spaceMD),
            Text(
              'No Holdings Yet',
              style: AppTextStyles.titleLarge.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: AppDimensions.spaceXS),
            Text(
              'Start investing to see your portfolio here',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textHint,
              ),
            ),
          ],
        ),
      );
    }

    return StyledCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          StyledSectionHeader(
            title: 'Your Holdings',
            subtitle: '${data.holdings.length} positions',
          ),
          const SizedBox(height: AppDimensions.spaceMD),
          ...data.holdings.map((holding) => _buildHoldingTile(holding)),
        ],
      ),
    );
  }

  Widget _buildHoldingTile(PortfolioHolding holding) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.all(AppDimensions.paddingMD),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(AppDimensions.radiusSM),
      ),
      child: Row(
        children: [
          // Stock symbol avatar
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: AppColors.primaryGradient,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(AppDimensions.radiusSM),
            ),
            child: Center(
              child: Text(
                holding.symbol.substring(0, 2).toUpperCase(),
                style: AppTextStyles.labelMedium.copyWith(
                  color: AppColors.textOnPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: AppDimensions.spaceMD),
          
          // Stock info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(holding.symbol, style: AppTextStyles.titleMedium),
                Text(
                  '${holding.shares} shares @ \$${holding.avgCostPerShare.toStringAsFixed(2)}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          
          // Performance
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '\$${holding.currentValue.toStringAsFixed(2)}',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${holding.gainLoss >= 0 ? '+' : ''}\$${holding.gainLoss.toStringAsFixed(2)} (${holding.gainLossPercent >= 0 ? '+' : ''}${holding.gainLossPercent.toStringAsFixed(2)}%)',
                style: AppTextStyles.bodySmall.copyWith(
                  color: holding.gainLoss >= 0 ? AppColors.success : AppColors.error,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
