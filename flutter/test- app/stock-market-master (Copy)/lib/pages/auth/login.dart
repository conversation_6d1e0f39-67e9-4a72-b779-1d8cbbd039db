import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stock_market/Bloc/bloc.dart';
import 'package:stock_market/Bloc/events.dart';
import 'package:stock_market/class/user.dart';
import 'package:stock_market/db/db_shared.dart';
import 'package:stock_market/db/db_sql.dart';
import 'package:stock_market/show_message.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});
  @override
  State<LoginPage> createState() => _LoginPage();
}

class _LoginPage extends State<LoginPage> {
  // Controller for the username field
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _password = TextEditingController();

  // Function to handle authentication with username
  Future<void> _signInWithusername() async {
    String username = _usernameController.text.trim();
    String password = _password.text.trim();
    if (username.isEmpty) {
      showSnackBar("username is empty", Colors.red, context);
      return;
    }
    if (password.isEmpty) {
      showSnackBar("Password is empty", Colors.red, context);
      return;
    }

    List<User> users = await loadUserData();

    bool userExist = users.any((u) => u.username == username);

    if (!userExist) {
      showSnackBar("User doesn't exist", Colors.red, context);
      return;
    }

    bool passwordCorrect =
        users.any((u) => u.username == username && u.password == password);

    if (!passwordCorrect) {
      showSnackBar("Password is'nt correct", Colors.red, context);
      return;
    }

    await saveCookieUser(username);

    BlocProvider.of<MarketBloc>(context).add(
      ChangeStateWithTransition(
        stateNext: StateApp.market,
        stateNow: StateApp.login,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Title
                Text(
                  'Connect to Market',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 30),
                // Username field
                TextField(
                  controller: _usernameController,
                  keyboardType: TextInputType.name,
                  decoration: InputDecoration(
                    labelText: 'username',
                    hintText: 'Enter your username',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                SizedBox(height: 20),
                TextField(
                  controller: _password,
                  obscureText: true,
                  decoration: InputDecoration(
                    labelText: 'Password',
                    hintText: 'Enter your password',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                SizedBox(height: 20),
                // Login button with username
                ElevatedButton(
                  onPressed: _signInWithusername,
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.symmetric(
                        vertical: 20.0, horizontal: 40.0),
                  ),
                  child: Text(
                    'Login',
                  ),
                ),
                SizedBox(height: 10),

                ElevatedButton(
                  onPressed: () {
                    BlocProvider.of<MarketBloc>(context)
                        .add(ChangeState(stateNext: StateApp.register));
                  },
                  child: Text("No register?"),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
