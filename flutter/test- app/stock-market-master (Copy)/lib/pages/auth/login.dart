import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stock_market/Bloc/bloc.dart';
import 'package:stock_market/Bloc/events.dart';
import 'package:stock_market/class/user.dart';
import 'package:stock_market/db/db_shared.dart';
import 'package:stock_market/db/db_sql.dart';
import 'package:stock_market/show_message.dart';
import 'package:stock_market/theme/app_colors.dart';
import 'package:stock_market/theme/app_dimensions.dart';
import 'package:stock_market/theme/app_text_styles.dart';
import 'package:stock_market/widgets/styled_components.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});
  @override
  State<LoginPage> createState() => _LoginPage();
}

class _LoginPage extends State<LoginPage> {
  // Controller for the username field
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _password = TextEditingController();

  // Function to handle authentication with username
  Future<void> _signInWithusername() async {
    String username = _usernameController.text.trim();
    String password = _password.text.trim();

    if (username.isEmpty) {
      if (mounted) {
        showSnackBar("Username is empty", Colors.red, context);
      }
      return;
    }
    if (password.isEmpty) {
      if (mounted) {
        showSnackBar("Password is empty", Colors.red, context);
      }
      return;
    }

    List<User> users = await loadUserData();

    bool userExist = users.any((u) => u.username == username);

    if (!userExist) {
      if (mounted) {
        showSnackBar("User doesn't exist", Colors.red, context);
      }
      return;
    }

    bool passwordCorrect =
        users.any((u) => u.username == username && u.password == password);

    if (!passwordCorrect) {
      if (mounted) {
        showSnackBar("Password isn't correct", Colors.red, context);
      }
      return;
    }

    await saveCookieUser(username);

    if (mounted) {
      BlocProvider.of<MarketBloc>(context).add(
        ChangeStateWithTransition(
          stateNext: StateApp.market,
          stateNow: StateApp.login,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppDimensions.paddingLG),
          child: StyledCard(
            padding: const EdgeInsets.all(AppDimensions.paddingXL),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // App Icon/Logo placeholder
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: AppColors.primaryGradient,
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(AppDimensions.radiusXL),
                  ),
                  child: const Icon(
                    Icons.trending_up,
                    size: AppDimensions.iconXL,
                    color: AppColors.textOnPrimary,
                  ),
                ),
                const SizedBox(height: AppDimensions.spaceXL),

                // Title
                Text(
                  'Welcome Back',
                  style: AppTextStyles.displayMedium,
                ),
                const SizedBox(height: AppDimensions.spaceSM),
                Text(
                  'Connect to your market account',
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: AppDimensions.spaceXXL),

                // Username field
                StyledTextField(
                  controller: _usernameController,
                  labelText: 'Username',
                  hintText: 'Enter your username',
                  keyboardType: TextInputType.name,
                  prefixIcon: const Icon(Icons.person_outline),
                ),
                const SizedBox(height: AppDimensions.spaceLG),

                // Password field
                StyledTextField(
                  controller: _password,
                  labelText: 'Password',
                  hintText: 'Enter your password',
                  obscureText: true,
                  prefixIcon: const Icon(Icons.lock_outline),
                ),
                const SizedBox(height: AppDimensions.spaceXXL),

                // Login button
                StyledGradientButton(
                  text: 'Sign In',
                  onPressed: _signInWithusername,
                  width: double.infinity,
                  icon: Icons.login,
                ),
                const SizedBox(height: AppDimensions.spaceLG),

                // Register button
                TextButton(
                  onPressed: () {
                    BlocProvider.of<MarketBloc>(context)
                        .add(ChangeState(stateNext: StateApp.register));
                  },
                  child: RichText(
                    text: TextSpan(
                      text: "Don't have an account? ",
                      style: AppTextStyles.bodyMedium,
                      children: [
                        TextSpan(
                          text: 'Sign Up',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
