import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stock_market/Bloc/bloc.dart';
import 'package:stock_market/Bloc/events.dart';
import 'package:stock_market/class/user.dart';
import 'package:stock_market/db/db_sql.dart';
import 'package:stock_market/show_message.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  bool succes = false;

  Future<void> _register() async {
    String username = _usernameController.text.trim();
    String password = _passwordController.text.trim();
    String confirmPassword = _confirmPasswordController.text.trim();

    if (username.isEmpty || password.isEmpty || confirmPassword.isEmpty) {
      showSnackBar('All fields are required.', Colors.red, context);
      return;
    }

    if (password != confirmPassword) {
      showSnackBar('Passwords do not match.', Colors.red, context);
      return;
    }

    try {
      // Check if the user already exists
      List<User> users = await loadUserData();
      bool userExists = users.any((user) => user.username == username);

      if (userExists) {
        showSnackBar('User is already registered.', Colors.red, context);
        return;
      }

      // Create the new user
      User newUser =
          User(username: username, password: password, wallet: **********);
      await saveUser(newUser);

      showSnackBar('Registration successful!', Colors.green, context);

      BlocProvider.of<MarketBloc>(context).add(ChangeStateWithTransition(
          stateNext: StateApp.login, stateNow: StateApp.register));
      print("Registration successful with: $username");
    } catch (e) {
      showSnackBar('An error occurred: $e', Colors.red, context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: succes
              ? Padding(
                  padding: const EdgeInsets.only(top: 20.0),
                  child: Text(
                    "You have been register!\nYou can now log in!",
                    style: TextStyle(fontSize: 18, color: Colors.green),
                  ),
                )
              : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Title
                    Text(
                      "Create account",
                      style:
                          TextStyle(fontSize: 26, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 20),

                    // Username field
                    TextField(
                      controller: _usernameController,
                      keyboardType: TextInputType.name,
                      decoration: InputDecoration(
                        labelText: "username",
                        hintText: "Enter your username",
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    const SizedBox(height: 15),

                    // Password field
                    TextField(
                      controller: _passwordController,
                      obscureText: true,
                      decoration: InputDecoration(
                        labelText: "Password",
                        hintText: "Enter password",
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    const SizedBox(height: 15),

                    // Confirmation password field
                    TextField(
                      controller: _confirmPasswordController,
                      obscureText: true,
                      decoration: InputDecoration(
                        labelText: "Confirm password",
                        hintText: "Retap password",
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    const SizedBox(height: 15),

                    // Register button
                    ElevatedButton(
                      onPressed: _register,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blueAccent,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(
                            vertical: 15, horizontal: 40),
                      ),
                      child: Text(
                        "Register",
                        style: TextStyle(fontSize: 18, color: Colors.white),
                      ),
                    ),

                    const SizedBox(height: 20),

                    ElevatedButton(
                      onPressed: () {
                        BlocProvider.of<MarketBloc>(context)
                            .add(ChangeState(stateNext: StateApp.login));
                      },
                      child: Text("Already a account?"),
                    ),
                  ],
                ),
        ),
      ),
    );
  }
}
