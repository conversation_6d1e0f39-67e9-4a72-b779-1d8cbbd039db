import 'package:flutter/material.dart';
import 'package:stock_market/class/action.dart';
import 'package:stock_market/class/user.dart';
import 'package:stock_market/db/db_shared.dart';
import 'package:stock_market/db/db_sql.dart';
import 'package:stock_market/pages/wallet/wallet_actions.dart';
import 'package:stock_market/pages/wallet/wallet_info_card.dart';
import 'package:tuple/tuple.dart';

class WalletWidget extends StatefulWidget {
  const WalletWidget({super.key});

  @override
  State<WalletWidget> createState() => _WalletWidgetState();
}

class _WalletWidgetState extends State<WalletWidget> {
  late Future<Tuple2<User?, List<ActionDB>>> userDataFuture;

  @override
  void initState() {
    super.initState();
    userDataFuture = _loadUserData();
  }

  Future<Tuple2<User?, List<ActionDB>>> _loadUserData() async {
    String username = await loadUserCookie();

    if (username.isEmpty) {
      return Tuple2(null, []);
    }

    List<User> users = await loadUserData(user: username);
    User? user = users.isNotEmpty ? users.first : null;

    List<ActionDB> actions = user != null ? await loadActionsUser() : [];

    return Tuple2(user, actions);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Tuple2<User?, List<ActionDB>>>(
      future: userDataFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        } else if (snapshot.hasError || snapshot.data == null) {
          return const Center(child: Text("Aucun utilisateur trouvé"));
        }

        User? user = snapshot.data!.item1;
        List<ActionDB> actions = snapshot.data!.item2;

        if (user == null) {
          return const Center(
              child: Text("Erreur lors du chargement de l'utilisateur"));
        }

        return SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              WalletInfoCard(username: user.username, wallet: user.wallet),
              const SizedBox(height: 10),
              // Use a Container to explicitly define the height if needed
              SizedBox(
                child: WalletActions(actions: actions),
              ),
            ],
          ),
        );
      },
    );
  }
}
