import 'package:flutter/material.dart';
import 'package:stock_market/class/action.dart';
import 'package:stock_market/class/user.dart';
import 'package:stock_market/db/db_shared.dart';
import 'package:stock_market/db/db_sql.dart';
import 'package:stock_market/pages/wallet/wallet_actions.dart';
import 'package:stock_market/pages/wallet/wallet_info_card.dart';
import 'package:stock_market/pages/portfolio/portfolio_dashboard.dart';
import 'package:stock_market/theme/app_colors.dart';
import 'package:stock_market/theme/app_dimensions.dart';
import 'package:stock_market/theme/app_text_styles.dart';
import 'package:tuple/tuple.dart';

class WalletWidget extends StatefulWidget {
  const WalletWidget({super.key});

  @override
  State<WalletWidget> createState() => _WalletWidgetState();
}

class _WalletWidgetState extends State<WalletWidget> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late Future<Tuple2<User?, List<ActionDB>>> userDataFuture;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    userDataFuture = _loadUserData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<Tuple2<User?, List<ActionDB>>> _loadUserData() async {
    String username = await loadUserCookie();

    if (username.isEmpty) {
      return Tuple2(null, []);
    }

    List<User> users = await loadUserData(user: username);
    User? user = users.isNotEmpty ? users.first : null;

    List<ActionDB> actions = user != null ? await loadActionsUser() : [];

    return Tuple2(user, actions);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Tab Bar
        Container(
          margin: const EdgeInsets.symmetric(
            horizontal: AppDimensions.marginMD,
            vertical: AppDimensions.marginSM,
          ),
          decoration: BoxDecoration(
            color: AppColors.surfaceVariant,
            borderRadius: BorderRadius.circular(AppDimensions.radiusMD),
          ),
          child: TabBar(
            controller: _tabController,
            indicator: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(AppDimensions.radiusMD),
            ),
            labelColor: AppColors.textOnPrimary,
            unselectedLabelColor: AppColors.textSecondary,
            labelStyle: AppTextStyles.labelLarge.copyWith(fontWeight: FontWeight.w600),
            unselectedLabelStyle: AppTextStyles.labelLarge,
            tabs: const [
              Tab(
                icon: Icon(Icons.analytics_outlined),
                text: 'Analytics',
              ),
              Tab(
                icon: Icon(Icons.list_outlined),
                text: 'Holdings',
              ),
            ],
          ),
        ),

        // Tab Views
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              // Analytics Tab - New Portfolio Dashboard
              const PortfolioDashboard(),

              // Holdings Tab - Original Wallet View
              FutureBuilder<Tuple2<User?, List<ActionDB>>>(
                future: userDataFuture,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (snapshot.hasError || snapshot.data == null) {
                    return const Center(child: Text("Unable to load user data"));
                  }

                  User? user = snapshot.data!.item1;
                  List<ActionDB> actions = snapshot.data!.item2;

                  if (user == null) {
                    return const Center(
                        child: Text("Error loading user data"));
                  }

                  return SingleChildScrollView(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        WalletInfoCard(username: user.username, wallet: user.wallet),
                        const SizedBox(height: AppDimensions.spaceMD),
                        SizedBox(
                          child: WalletActions(actions: actions),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
