import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stock_market/Bloc/bloc.dart';
import 'package:stock_market/Bloc/events.dart';
import 'package:stock_market/class/action.dart';

class WalletActions extends StatelessWidget {
  final List<ActionDB> actions;

  const WalletActions({required this.actions, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    void redirect(ActionStock action) {
      BlocProvider.of<MarketBloc>(context).add(
        ChangeStateWithTransition(
            stateNext: StateApp.actionDetails,
            stateNow: StateApp.market,
            action: action),
      );
    }

    return SizedBox(
      height: 450, // Fixed height for the ListView
      child: actions.isNotEmpty
          ? ListView.builder(
              itemCount: actions.length,
              itemBuilder: (context, index) {
                final action = actions[index];
                return GestureDetector(
                  onTap: () async {
                    ActionStock stockAction = await action.toActionStock();
                    redirect(stockAction);
                  },
                  child: Card(
                    margin: const EdgeInsets.symmetric(vertical: 8),
                    elevation: 5,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: ListTile(
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      leading: Icon(
                        Icons.trending_up,
                        color: Colors.green[700],
                      ),
                      title: Text(
                        action.name,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                      subtitle: Text(
                        "Amount: ${action.count}",
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[700],
                        ),
                      ),
                      trailing: Text(
                        "${(action.count * action.price).toStringAsFixed(2)} \$", // Total purchase value
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.blueAccent,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                );
              },
            )
          : noAction(),
    );
  }
}

Padding noAction() {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 20),
    child: Container(
      decoration: BoxDecoration(
        color: Colors.blueAccent.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.blueAccent.withValues(alpha: 0.15),
          width: 1.5,
        ),
      ),
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
      child: Text(
        "It looks like you don't own any actions yet.\nHead to the market and start building your portfolio!",
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w400,
          color: Colors.grey[700],
          height: 1.6,
        ),
        textAlign: TextAlign.center,
      ),
    ),
  );
}
