import 'package:flutter/material.dart';

class WalletInfoCard extends StatelessWidget {
  final String username;
  final double wallet;

  const WalletInfoCard(
      {super.key, required this.username, required this.wallet});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2, // Add a subtle shadow
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      color: Colors.white10.withValues(alpha: 0.95), // Light white color
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person, color: Colors.blue, size: 28),
                const SizedBox(width: 10),
                Text(
                  username,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const Divider(thickness: 1.2, height: 20),
            Row(
              children: [
                const Icon(Icons.account_balance_wallet,
                    color: Colors.green, size: 28),
                const SizedBox(width: 10),
                Text(
                  "${wallet.toStringAsFixed(2)} \$",
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
