import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class HistoryGraph extends StatelessWidget {
  final List<(double, DateTime)> history;
  const HistoryGraph({required this.history, super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 300,
      child: history.isEmpty
          ? loadGraph()
          : LineChart(
              LineChartData(
                gridData: FlGridData(
                  show: true,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: Colors.grey.withValues(alpha: 0.5),
                      strokeWidth: 1,
                    );
                  },
                  getDrawingVerticalLine: (value) {
                    return FlLine(
                      color: Colors.grey.withValues(alpha: 0.5),
                      strokeWidth: 1,
                    );
                  },
                ),
                titlesData: FlTitlesData(
                  show: true,
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, titleMeta) {
                        return Text(
                          value.toStringAsFixed(0),
                          style: const TextStyle(fontSize: 12),
                        );
                      },
                      reservedSize: 30,
                    ),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, titleMeta) {
                        return datePadding(history, value);
                      },
                      reservedSize: 30,
                    ),
                  ),

                  rightTitles: AxisTitles(),
                  topTitles:
                      AxisTitles(), // Explicitly add to disable top titles
                ),
                borderData: FlBorderData(
                  show: true,
                  border: Border.all(color: Colors.grey, width: 1),
                ),
                lineBarsData: [
                  LineChartBarData(
                    spots: history
                        .map((data) => FlSpot(
                              data.$2.millisecondsSinceEpoch.toDouble(),
                              data.$1,
                            ))
                        .toList(),
                    color: Colors.blueAccent,
                    barWidth: 3,
                    dotData: const FlDotData(show: false),
                  ),
                ],
                minY: _adjustMinY(history.isNotEmpty
                    ? history
                            .map((data) => data.$1)
                            .reduce((a, b) => a < b ? a : b) -
                        1
                    : 0),
                maxY: _adjustMaxY(history.isNotEmpty
                    ? history
                            .map((data) => data.$1)
                            .reduce((a, b) => a > b ? a : b) +
                        1
                    : 0),
              ),
            ),
    );
  }
}

double _adjustMaxY(double maxValue) {
  double nextMultipleOfFive = (maxValue / 5).ceilToDouble() * 5;
  return nextMultipleOfFive;
}

double _adjustMinY(double minValue) {
  double previousMultipleOfFive = (minValue / 5).floorToDouble() * 5;
  return previousMultipleOfFive;
}

Center loadGraph() {
  return const Center(
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        CircularProgressIndicator(),
        SizedBox(height: 10),
        Text("Loading data...", style: TextStyle(color: Colors.grey)),
      ],
    ),
  );
}

Widget datePadding(List<(double, DateTime)> history, double value) {
  // Convert the value to a date
  DateTime date = DateTime.fromMillisecondsSinceEpoch(value.toInt());
  String formattedDate;
  if (history.any((v) => v.$2.year != date.year)) {
    formattedDate = DateFormat('yyyy').format(date);
  } else {
    formattedDate = DateFormat('MM-dd').format(date);
  }

  // Check if the value is the first or last date
  int firstIndex = history.first.$2.millisecondsSinceEpoch.toInt();
  int lastIndex = history.last.$2.millisecondsSinceEpoch.toInt();

  // Display only the first and last date
  if (value.toInt() == firstIndex) {
    return Padding(
      padding:
          const EdgeInsets.only(left: 30.0), // Shift slightly to the bottom
      child: Text(
        formattedDate,
        style: const TextStyle(fontSize: 12),
      ),
    );
  } else if (value.toInt() == lastIndex) {
    return Padding(
      padding:
          const EdgeInsets.only(right: 30.0), // Shift slightly to the bottom
      child: Text(
        formattedDate,
        style: const TextStyle(fontSize: 12),
      ),
    );
  } else {
    return const SizedBox(); // Do not display for other dates
  }
}
