import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stock_market/Bloc/bloc.dart';
import 'package:stock_market/Bloc/events.dart';
import 'package:stock_market/class/action.dart';
import 'package:stock_market/pages/button_buy.dart';

// StockTile widget extracted for better reusability and performance
class StockTile extends StatelessWidget {
  final ActionStock action;
  final VoidCallback onTap;

  const StockTile({
    super.key,
    required this.action,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        title: Hero(
          tag: 'stock_name_${action.name}',
          child: Material(
            color: Colors.transparent,
            child: Text(
              action.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        ),
        subtitle: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Hero(
                tag: 'stock_rate_${action.name}',
                child: Material(
                  color: Colors.transparent,
                  child: Text(
                    "${action.rate.toStringAsFixed(2)}\$",
                    style: const TextStyle(fontSize: 18),
                  ),
                ),
              ),
              Hero(
                tag: 'stock_currency_${action.name}',
                child: Material(
                  color: Colors.transparent,
                  child: Text(
                    action.currency,
                    style: const TextStyle(
                      color: Colors.blue,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        trailing: ButtonBuy(action: action),
        onTap: onTap,
      ),
    );
  }
}

class RandomActions extends StatelessWidget {
  final List<ActionStock> actions;
  
  const RandomActions({required this.actions, super.key});

  @override
  Widget build(BuildContext context) {
    if (actions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.warning_amber_rounded, size: 48, color: Colors.orange),
            SizedBox(height: 16),
            Text(
              "No available actions",
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: actions.length,
      itemBuilder: (context, index) {
        final action = actions[index];
        return StockTile(
          key: ValueKey(action.name),
          action: action,
          onTap: () {
            BlocProvider.of<MarketBloc>(context).add(
              ChangeStateWithTransition(
                stateNext: StateApp.actionDetails,
                stateNow: StateApp.market,
                action: action,
              ),
            );
          },
        );
      },
    );
  }
}

List<ActionStock> randomListActions(List<ActionStock> actions) {
  if (actions.isEmpty) return [];
  
  final random = Random();
  final shuffledActions = List<ActionStock>.from(actions)..shuffle(random);
  
  // Take at most 3 actions or all available if less than 3
  final count = min(3, shuffledActions.length);
  return shuffledActions.take(count).toList();
}
