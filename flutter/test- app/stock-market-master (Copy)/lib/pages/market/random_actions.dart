import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stock_market/Bloc/bloc.dart';
import 'package:stock_market/Bloc/events.dart';
import 'package:stock_market/class/action.dart';
import 'package:stock_market/pages/button_buy.dart';
import 'package:stock_market/theme/app_colors.dart';
import 'package:stock_market/theme/app_dimensions.dart';
import 'package:stock_market/theme/app_text_styles.dart';
import 'package:stock_market/widgets/styled_components.dart';

// Enhanced StockTile widget with modern styling
class StockTile extends StatelessWidget {
  final ActionStock action;
  final VoidCallback onTap;

  const StockTile({
    super.key,
    required this.action,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return StyledCard(
      margin: const EdgeInsets.symmetric(vertical: AppDimensions.marginSM),
      padding: const EdgeInsets.all(AppDimensions.stockTilePadding),
      onTap: onTap,
      child: Row(
        children: [
          // Stock icon/avatar
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: AppColors.primaryGradient,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(AppDimensions.radiusMD),
            ),
            child: Center(
              child: Text(
                action.name.substring(0, min(2, action.name.length)).toUpperCase(),
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.textOnPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: AppDimensions.spaceMD),

          // Stock info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Hero(
                  tag: 'stock_name_${action.name}',
                  child: Material(
                    color: Colors.transparent,
                    child: Text(
                      action.name,
                      style: AppTextStyles.stockName,
                    ),
                  ),
                ),
                const SizedBox(height: AppDimensions.spaceXS),
                Hero(
                  tag: 'stock_currency_${action.name}',
                  child: Material(
                    color: Colors.transparent,
                    child: Text(
                      action.currency,
                      style: AppTextStyles.stockSymbol,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Price and buy button
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Hero(
                tag: 'stock_rate_${action.name}',
                child: Material(
                  color: Colors.transparent,
                  child: Text(
                    "\$${action.rate.toStringAsFixed(2)}",
                    style: AppTextStyles.stockPrice,
                  ),
                ),
              ),
              const SizedBox(height: AppDimensions.spaceSM),
              ButtonBuy(action: action),
            ],
          ),
        ],
      ),
    );
  }
}

class RandomActions extends StatelessWidget {
  final List<ActionStock> actions;
  
  const RandomActions({required this.actions, super.key});

  @override
  Widget build(BuildContext context) {
    if (actions.isEmpty) {
      return StyledCard(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.trending_up_outlined,
              size: AppDimensions.iconXL,
              color: AppColors.textHint,
            ),
            const SizedBox(height: AppDimensions.spaceMD),
            Text(
              "No stocks available",
              style: AppTextStyles.titleLarge.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: AppDimensions.spaceXS),
            Text(
              "Check back later for new opportunities",
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textHint,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: actions.length,
      itemBuilder: (context, index) {
        final action = actions[index];
        return StockTile(
          key: ValueKey(action.name),
          action: action,
          onTap: () {
            BlocProvider.of<MarketBloc>(context).add(
              ChangeStateWithTransition(
                stateNext: StateApp.actionDetails,
                stateNow: StateApp.market,
                action: action,
              ),
            );
          },
        );
      },
    );
  }
}

List<ActionStock> randomListActions(List<ActionStock> actions) {
  if (actions.isEmpty) return [];
  
  final random = Random();
  final shuffledActions = List<ActionStock>.from(actions)..shuffle(random);
  
  // Take at most 3 actions or all available if less than 3
  final count = min(3, shuffledActions.length);
  return shuffledActions.take(count).toList();
}
