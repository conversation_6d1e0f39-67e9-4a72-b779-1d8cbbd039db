import 'dart:async';

import 'package:flutter/material.dart';
import 'package:marquee/marquee.dart';
import 'package:stock_market/class/action.dart';
import 'package:stock_market/pages/market/random_actions.dart';
import 'package:stock_market/theme/app_colors.dart';
import 'package:stock_market/theme/app_dimensions.dart';
import 'package:stock_market/theme/app_text_styles.dart';
import 'package:stock_market/theme/app_theme.dart';
import 'package:stock_market/widgets/styled_components.dart';
import 'package:tuple/tuple.dart';

// Loading skeleton for market items
class MarketItemSkeleton extends StatelessWidget {
  const MarketItemSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: AppDimensions.marginSM),
      child: Column(
        children: List.generate(3, (index) =>
          Container(
            height: AppDimensions.stockTileHeight,
            margin: const EdgeInsets.only(bottom: AppDimensions.marginSM),
            child: StyledSkeleton(
              width: double.infinity,
              height: AppDimensions.stockTileHeight,
              borderRadius: BorderRadius.circular(AppDimensions.stockTileRadius),
            ),
          ),
        ),
      ),
    );
  }
}

class MarketPlace extends StatefulWidget {
  const MarketPlace({super.key});

  @override
  State<MarketPlace> createState() => _MarketPlaceState();
}

class _MarketPlaceState extends State<MarketPlace> {
  late Future<Tuple2<List<String>, List<ActionStock>>> futureData;
  late List<ActionStock> randomActionOne;
  late List<ActionStock> randomActionTwo;
  Timer? _timer;
  bool _isRefreshing = false;
  DateTime _lastRefreshTime = DateTime.now();
  static const refreshCooldown = Duration(milliseconds: 500); // Debounce duration

  @override
  void initState() {
    super.initState();

    // Initialize futureData with stock data
    futureData = StockService().fetchStocksList().then((stocks) async {
      // Filter out unnecessary actions
      List<String> filteredStocks =
          stocks.where((fs) => fs != "CUZ" && fs != "GLT").toList();
      filteredStocks.shuffle();

      List<String> selectedStocks = filteredStocks.take(10).toList();

      List<Future<ActionStock>> futures = selectedStocks
          .map((fs) => StockService().fetchStockData(fs))
          .toList();

      List<ActionStock> actions = await Future.wait(futures);

      return Tuple2(
          stocks, actions); // Return the stocks and actions retrieved
    });

    futureData.then((data) {
      setState(() {
        randomActionOne = randomListActions(data.item2);
        randomActionTwo = randomListActions(data.item2);
      });
    });

    _startActionsUpdates();
  }

  // Start updating user's position periodically
  void _startActionsUpdates() {
    _timer = Timer.periodic(const Duration(seconds: 2), (timer) {
      _refreshRates();
    });
  }

  // Function to update only the rates of actions with debouncing
  Future<void> _refreshRates() async {
    // Check if we're already refreshing or if it's too soon since last refresh
    if (_isRefreshing || DateTime.now().difference(_lastRefreshTime) < refreshCooldown) {
      return;
    }

    try {
      _isRefreshing = true;
      
      // Wait for futureData to be resolved to access the data
      final Tuple2<List<String>, List<ActionStock>> data = await futureData;

      // Update rates in parallel using Future.wait
      await Future.wait(
        data.item2.map((a) => a.updateRate()),
      );

      if (mounted) {
        setState(() {
          _lastRefreshTime = DateTime.now();
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to update rates. Retrying...'),
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _refreshRates,
            ),
          ),
        );
      }
    } finally {
      _isRefreshing = false;
    }
  }

  @override
  void dispose() {
    _timer?.cancel(); // Cancel the timer if it exists
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FutureBuilder<Tuple2<List<String>, List<ActionStock>>>(
        future: futureData,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return ListView(
              padding: const EdgeInsets.all(16),
              children: const [
                MarketItemSkeleton(),
                SizedBox(height: 16),
                MarketItemSkeleton(),
              ],
            );
          }

          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 16),
                  Text('Error: ${snapshot.error}'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        futureData = StockService().fetchStocksList().then((stocks) async {
                          List<String> filteredStocks = stocks.where((fs) => fs != "CUZ" && fs != "GLT").toList();
                          filteredStocks.shuffle();
                          List<String> selectedStocks = filteredStocks.take(10).toList();
                          List<ActionStock> actions = await Future.wait(
                            selectedStocks.map((fs) => StockService().fetchStockData(fs))
                          );
                          return Tuple2(stocks, actions);
                        });
                      });
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (!snapshot.hasData) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.info_outline, size: 48),
                  SizedBox(height: 16),
                  Text("No data available"),
                ],
              ),
            );
          }

          // Extract the data
          List<String> stocks = snapshot.data!.item1;

          return ListView(
            padding: const EdgeInsets.all(AppDimensions.paddingMD),
            children: [
              // Stock market banner with colors
              stockMarketBanner(stocks),

              const SizedBox(height: AppDimensions.spaceLG),

              // Popular actions section
              StyledSectionHeader(
                title: 'Trending Now',
                subtitle: 'Most active stocks today',
              ),
              const SizedBox(height: AppDimensions.spaceMD),

              // Popular actions list
              RandomActions(
                  actions: randomActionOne), // Pass the retrieved actions

              const SizedBox(height: AppDimensions.spaceXXL),

              // Random actions section
              StyledSectionHeader(
                title: 'Market Picks',
                subtitle: 'Discover new opportunities',
              ),
              const SizedBox(height: AppDimensions.spaceMD),

              // Random actions list
              RandomActions(actions: randomActionTwo),

              // Add some bottom padding
              const SizedBox(height: AppDimensions.spaceXL),
            ],
          );
        },
      ),
    );
  }
}

// Enhanced stock market banner with modern styling
Widget stockMarketBanner(List<String> stocks) {
  return Container(
    margin: const EdgeInsets.symmetric(vertical: AppDimensions.marginSM),
    height: AppDimensions.bannerHeight,
    decoration: BoxDecoration(
      gradient: const LinearGradient(
        colors: AppColors.marketBannerGradient,
        begin: Alignment.centerLeft,
        end: Alignment.centerRight,
      ),
      borderRadius: BorderRadius.circular(AppDimensions.bannerRadius),
      boxShadow: AppTheme.cardShadow,
    ),
    child: ClipRRect(
      borderRadius: BorderRadius.circular(AppDimensions.bannerRadius),
      child: Center(
        child: Marquee(
          text: stocks.join("  •  "), // Separate stocks with bullet points
          style: AppTextStyles.bannerText,
          scrollAxis: Axis.horizontal,
          crossAxisAlignment: CrossAxisAlignment.center,
          blankSpace: 200.0,
          velocity: 50.0,
          pauseAfterRound: const Duration(seconds: 1),
          startPadding: 10.0,
        ),
      ),
    ),
  );
}
