import 'dart:async';

import 'package:flutter/material.dart';
import 'package:marquee/marquee.dart';
import 'package:stock_market/class/action.dart';
import 'package:stock_market/pages/market/random_actions.dart';
import 'package:tuple/tuple.dart';

// Loading skeleton for market items
class MarketItemSkeleton extends StatelessWidget {
  const MarketItemSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        children: List.generate(3, (index) => 
          Container(
            height: 60,
            margin: const EdgeInsets.only(bottom: 8.0),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
    );
  }
}

class MarketPlace extends StatefulWidget {
  const MarketPlace({super.key});

  @override
  State<MarketPlace> createState() => _MarketPlaceState();
}

class _MarketPlaceState extends State<MarketPlace> {
  late Future<Tuple2<List<String>, List<ActionStock>>> futureData;
  late List<ActionStock> randomActionOne;
  late List<ActionStock> randomActionTwo;
  Timer? _timer;
  bool _isRefreshing = false;
  DateTime _lastRefreshTime = DateTime.now();
  static const refreshCooldown = Duration(milliseconds: 500); // Debounce duration

  @override
  void initState() {
    super.initState();

    // Initialize futureData with stock data
    futureData = StockService().fetchStocksList().then((stocks) async {
      // Filter out unnecessary actions
      List<String> filteredStocks =
          stocks.where((fs) => fs != "CUZ" && fs != "GLT").toList();
      filteredStocks.shuffle();

      List<String> selectedStocks = filteredStocks.take(10).toList();

      List<Future<ActionStock>> futures = selectedStocks
          .map((fs) => StockService().fetchStockData(fs))
          .toList();

      List<ActionStock> actions = await Future.wait(futures);

      return Tuple2(
          stocks, actions); // Return the stocks and actions retrieved
    });

    futureData.then((data) {
      setState(() {
        randomActionOne = randomListActions(data.item2);
        randomActionTwo = randomListActions(data.item2);
      });
    });

    _startActionsUpdates();
  }

  // Start updating user's position periodically
  void _startActionsUpdates() {
    _timer = Timer.periodic(const Duration(seconds: 2), (timer) {
      _refreshRates();
    });
  }

  // Function to update only the rates of actions with debouncing
  Future<void> _refreshRates() async {
    // Check if we're already refreshing or if it's too soon since last refresh
    if (_isRefreshing || DateTime.now().difference(_lastRefreshTime) < refreshCooldown) {
      return;
    }

    try {
      _isRefreshing = true;
      
      // Wait for futureData to be resolved to access the data
      final Tuple2<List<String>, List<ActionStock>> data = await futureData;

      // Update rates in parallel using Future.wait
      await Future.wait(
        data.item2.map((a) => a.updateRate()),
      );

      if (mounted) {
        setState(() {
          _lastRefreshTime = DateTime.now();
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to update rates. Retrying...'),
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _refreshRates,
            ),
          ),
        );
      }
    } finally {
      _isRefreshing = false;
    }
  }

  @override
  void dispose() {
    _timer?.cancel(); // Cancel the timer if it exists
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FutureBuilder<Tuple2<List<String>, List<ActionStock>>>(
        future: futureData,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return ListView(
              padding: const EdgeInsets.all(16),
              children: const [
                MarketItemSkeleton(),
                SizedBox(height: 16),
                MarketItemSkeleton(),
              ],
            );
          }

          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 16),
                  Text('Error: ${snapshot.error}'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        futureData = StockService().fetchStocksList().then((stocks) async {
                          List<String> filteredStocks = stocks.where((fs) => fs != "CUZ" && fs != "GLT").toList();
                          filteredStocks.shuffle();
                          List<String> selectedStocks = filteredStocks.take(10).toList();
                          List<ActionStock> actions = await Future.wait(
                            selectedStocks.map((fs) => StockService().fetchStockData(fs))
                          );
                          return Tuple2(stocks, actions);
                        });
                      });
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (!snapshot.hasData) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.info_outline, size: 48),
                  SizedBox(height: 16),
                  Text("No data available"),
                ],
              ),
            );
          }

          // Extract the data
          List<String> stocks = snapshot.data!.item1;

          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Stock market banner with colors
              stockMarketBanner(stocks),

              const SizedBox(height: 16),

              // Popular actions title
              Text(
                'Actions of the moments',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 16),

              // Popular actions list
              RandomActions(
                  actions: randomActionOne), // Pass the retrieved actions

              const SizedBox(height: 32),
              // Random actions title
              Text(
                'Random Actions',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),

              const SizedBox(height: 16),

              // Random actions list
              RandomActions(actions: randomActionTwo),
            ],
          );
        },
      ),
    );
  }
}

//Purely aesthetic but it's good enough
Padding stockMarketBanner(List<String> stocks) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 8),
    child: SizedBox(
      height: 40,
      child: DecoratedBox(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.blue,
              Colors.green,
              Colors.purple
            ], // Color gradient
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
        ),
        child: Center(
          child: Marquee(
            text:
                stocks.join("  |  "), // Separate actions with | symbols
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white, // Text color (white)
            ),
            scrollAxis: Axis.horizontal,
            crossAxisAlignment: CrossAxisAlignment.start,
            blankSpace: 200.0,
            velocity: 50.0,
            pauseAfterRound: const Duration(seconds: 1),
            startPadding: 10.0,
          ),
        ),
      ),
    ),
  );
}
