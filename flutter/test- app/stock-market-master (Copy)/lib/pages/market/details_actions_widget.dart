import 'dart:async';

import 'package:flutter/material.dart';
import 'package:stock_market/class/action.dart';
import 'package:stock_market/db/db_sql.dart';
import 'package:stock_market/pages/button_buy.dart';
import 'package:stock_market/pages/button_sell.dart';
import 'package:stock_market/pages/market/graph_history.dart';

class ActionDetailsWidget extends StatefulWidget {
  final ActionStock? action;
  const ActionDetailsWidget({required this.action, super.key});

  @override
  State<ActionDetailsWidget> createState() => _ActionDetailsWidgetState();
}

class _ActionDetailsWidgetState extends State<ActionDetailsWidget> {
  List<ActionDB?> actionPossess = [];
  final List<(double, DateTime)> historyToDay = [];
  Timer? _timer;
  Timer? _debounceTimer;
  bool _isLoading = false;
  bool _isUpdating = false;
  DateHistory _currentPeriod = DateHistory.oneWeek;
  static const _updateInterval = Duration(seconds: 2);
  static const _debounceDuration = Duration(milliseconds: 300);

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    setState(() => _isLoading = true);
    try {
      await Future.wait([
        _loadStockHistory(_currentPeriod),
        _updateCountAction(),
      ]);
      _startActionsUpdates();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to load data'),
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _initializeData,
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _startActionsUpdates() {
    _timer?.cancel();
    _timer = Timer.periodic(_updateInterval, (timer) {
      _debounceTimer?.cancel();
      _debounceTimer = Timer(_debounceDuration, _refreshRates);
    });
  }

  Future<void> _refreshRates() async {
    if (_isUpdating || widget.action == null) return;

    try {
      _isUpdating = true;
      await widget.action!.updateRate();
      if (mounted) {
        setState(() {
          historyToDay.add((widget.action!.rate, DateTime.now()));
          // Keep only last hour of data for performance
          if (historyToDay.length > 1800) { // 1800 = 1 hour of 2-second updates
            historyToDay.removeAt(0);
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to update rates'),
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _refreshRates,
            ),
          ),
        );
      }
    } finally {
      _isUpdating = false;
    }
  }

  Future<void> _updateCountAction() async {
    actionPossess = await loadActionUser(widget.action!.name);
    setState(() {});
  }

  Future<void> _loadStockHistory(DateHistory date) async {
    if (widget.action == null || _isLoading) return;

    setState(() {
      _isLoading = true;
      _currentPeriod = date;
    });

    try {
      if (date == DateHistory.oneDay) {
        widget.action!.history = historyToDay;
      } else {
        switch (date) {
          case DateHistory.oneWeek:
            await widget.action!.historyActionOneWeek();
            break;
          case DateHistory.oneMonth:
            await widget.action!.historyActionOneMonth();
            break;
          case DateHistory.oneYear:
            await widget.action!.historyActionOneYear();
            break;
          case DateHistory.fiveYear:
            await widget.action!.historyActionFiveYear();
            break;
          case DateHistory.tenYear:
            await widget.action!.historyActionTenYear();
            break;
          default:
            break;
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to load history'),
            action: SnackBarAction(
              label: 'Retry',
              onPressed: () => _loadStockHistory(date),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // If the action is null, display an error
    if (widget.action == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Détails de l\'action')),
        body: const Center(child: Text('Aucune action à afficher.')),
      );
    }

    final action = widget.action!;

    return Scaffold(
      body: SingleChildScrollView(
        // Make the page scrollable
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  action.name,
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Colors.indigo[900],
                    letterSpacing: 1.2,
                  ),
                ),
                const SizedBox(height: 6), // Subtle spacing

                Text(
                  action.currency,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[800], // Dark gray for readability and discretion
                  ),
                ),
                const SizedBox(height: 4),

                Text(
                  _formatDate(action.date),
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[
                        600], // More discreet to avoid visually overloading
                    fontStyle: FontStyle.italic, // Adds a touch of elegance
                  ),
                ),
                const SizedBox(height: 8),

                // Add the graph here
                _buildTextInfo(
                    'Price closure: ',
                    action.history.isNotEmpty
                        ? '${action.history.first.$1.toStringAsFixed(2)}\$ → ${action.history.last.$1.toStringAsFixed(2)}\$'
                        : '0',
                    fontSize: 14),

                const SizedBox(height: 16),

                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    // Check if actionPossess is not null
                    Column(
                      children: [
                        if (actionPossess.isNotEmpty)
                          for (ActionDB? a
                              in actionPossess.whereType<ActionDB>())
                            Icon(
                              action.rate >= a!.price
                                  ? Icons
                                      .arrow_upward // Icon for an increase
                                  : Icons
                                      .arrow_downward, // Icon for a decrease
                              color: action.rate >= a.price
                                  ? Colors.green // Green for an increase
                                  : Colors.red, // Red for a decrease
                              size: 20,
                            ),
                      ],
                    ),

                    Text(
                      "${action.rate.toStringAsFixed(2)} \$",
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: actionPossess.isEmpty
                            ? Colors.blueAccent
                            : (actionPossess.last!.price > action.rate
                                ? Colors.red
                                : Colors
                                    .green), // Dark green for a touch of financial reassurance
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 8),

            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              )
            else ...[
              if (actionPossess.isNotEmpty && action.history.isNotEmpty)
                ownShard(action, actionPossess),

              const SizedBox(height: 10),

              buttonHistoryGraph(
                _loadStockHistory,
                currentPeriod: _currentPeriod,
              ),

              const SizedBox(height: 10),

              // Placeholder for the graph (use a real graph widget here)
              HistoryGraph(
                history: action.history,
                key: ValueKey('${action.name}_${_currentPeriod}'),
              ),
            ],

            const SizedBox(height: 12),

            const Divider(),
            const SizedBox(height: 8),

            // Action Buttons (for example, buy or sell)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ButtonBuy(
                  action: action,
                  updateCountAction: _updateCountAction,
                ),
                if (actionPossess.isNotEmpty)
                  ButtonSell(
                    action: action,
                    updateAction: _updateCountAction,
                    actionPossess: actionPossess,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Helper function to build the text widget
  Widget _buildTextInfo(String label, String value,
      {double fontSize = 18, bool isBold = false}) {
    return Row(
      children: [
        Text(
          '$label ',
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.w500,
            color: Colors.grey[700],
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              color: Colors.black,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  // Helper function to format the date
  String _formatDate(DateTime date) {
    final formattedDate = '${date.day}/${date.month}/${date.year}';
    return formattedDate;
  }
}

class _PeriodButton extends StatelessWidget {
  final DateHistory period;
  final String label;
  final bool isSelected;
  final VoidCallback onPressed;

  const _PeriodButton({
    required this.period,
    required this.label,
    required this.isSelected,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: isSelected ? Theme.of(context).primaryColor : null,
          foregroundColor: isSelected ? Colors.white : null,
        ),
        child: Text(label),
      ),
    );
  }
}

Center buttonHistoryGraph(
  Function(DateHistory) loadStockHistory, {
  required DateHistory currentPeriod,
}) {
  return Center(
    child: SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _PeriodButton(
            period: DateHistory.oneDay,
            label: "Now",
            isSelected: currentPeriod == DateHistory.oneDay,
            onPressed: () => loadStockHistory(DateHistory.oneDay),
          ),
          _PeriodButton(
            period: DateHistory.oneWeek,
            label: "Week",
            isSelected: currentPeriod == DateHistory.oneWeek,
            onPressed: () => loadStockHistory(DateHistory.oneWeek),
          ),
          _PeriodButton(
            period: DateHistory.oneMonth,
            label: "Month",
            isSelected: currentPeriod == DateHistory.oneMonth,
            onPressed: () => loadStockHistory(DateHistory.oneMonth),
          ),
          _PeriodButton(
            period: DateHistory.oneYear,
            label: "Year",
            isSelected: currentPeriod == DateHistory.oneYear,
            onPressed: () => loadStockHistory(DateHistory.oneYear),
          ),
          _PeriodButton(
            period: DateHistory.fiveYear,
            label: "5 years",
            isSelected: currentPeriod == DateHistory.fiveYear,
            onPressed: () => loadStockHistory(DateHistory.fiveYear),
          ),
          _PeriodButton(
            period: DateHistory.tenYear,
            label: "10 years",
            isSelected: currentPeriod == DateHistory.tenYear,
            onPressed: () => loadStockHistory(DateHistory.tenYear),
          ),
        ],
      ),
    ),
  );
}

Column ownShard(ActionStock action, List<ActionDB?> actionPossess) {
  bool goodSell(ActionDB a) {
    return action.rate * a.count >= a.count * a.price;
  }

  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      // Main text
      Text(
        'You own:',
        style: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
      SizedBox(height: 4),

      // Dynamically generate the actions possessed
      ...actionPossess.map(
        (a) {
          if (a == null) return SizedBox();

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${a.count} shares for ${a.price.toStringAsFixed(2)}\$ each.',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.blueGrey[700],
                ),
              ),
              SizedBox(height: 16),

              Text(
                "Actual price if you sell: ${(action.history.last.$1 * a.count).toStringAsFixed(2)}\$",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: goodSell(a)
                      ? Colors.green
                      : Colors.red, // Color to attract attention
                ),
              ),
              SizedBox(height: 16), // Space between each action block
            ],
          );
        },
      ),
    ],
  );
}
