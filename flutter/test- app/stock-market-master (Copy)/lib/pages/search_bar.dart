import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stock_market/Bloc/bloc.dart';
import 'package:stock_market/Bloc/events.dart';
import 'package:stock_market/class/action.dart';
import 'package:stock_market/pages/market/random_actions.dart';

class SearchBarMarket extends StatefulWidget {
  const SearchBarMarket({super.key});
  @override
  _SearchBarMarket createState() => _SearchBarMarket();
}

class _SearchBarMarket extends State<SearchBarMarket> {
  final TextEditingController searchController = TextEditingController();
  List<String> allActions = []; // List of all action names
  List<ActionStock> filteredActions = []; // List of filtered actions
  List<String> suggestions = []; // List of suggestions based on the search
  bool isLoading = false;
  Timer? _debounceTimer;
  static const Duration _debounceDuration = Duration(milliseconds: 300);

  @override
  void initState() {
    super.initState();
    _loadActions();
  }

  void _loadActions() async {
    List<String> actionNames = await StockService().fetchStocksList();

    List<String> filteredStocks =
        actionNames.where((fs) => fs != "CUZ" && fs != "GLT").toList();

    setState(() {
      allActions = filteredStocks;
      suggestions = [];
      filteredActions = [];
    });
  }

  // Function to filter suggestions based on search
  void _filterSuggestions() {
    // Cancel existing timer if any
    _debounceTimer?.cancel();

    // Start new timer
    _debounceTimer = Timer(_debounceDuration, () {
      String query = searchController.text;

      if (query.isEmpty) {
        setState(() {
          suggestions = [];
        });
        return;
      }

      // Case-insensitive search with caching
      final queryLower = query.toLowerCase();
      List<String> filteredSuggestions = allActions
          .where((action) => action.toLowerCase().contains(queryLower))
          .take(5) // Limit suggestions to 5 items for better performance
          .toList();

      setState(() {
        suggestions = filteredSuggestions;
      });
    });
  }

  // Function to fetch filtered actions data
  Future<void> _fetchFilteredActions() async {
    String query = searchController.text.trim();

    if (query.isEmpty) {
      setState(() {
        filteredActions = [];
        suggestions = [];
      });
      return;
    }

    setState(() {
      isLoading = true;
      suggestions = [];
    });

    try {
      final queryLower = query.toLowerCase();
      final matchingStocks = allActions
          .where((name) => name.toLowerCase().contains(queryLower))
          .take(10) // Limit results for better performance
          .toList();

      final futures = matchingStocks.map((name) => 
        StockService().fetchStockData(name)
      );

      final actions = await Future.wait(futures);

      if (mounted) {
        setState(() {
          filteredActions = actions;
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to fetch stock data'),
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _fetchFilteredActions,
            ),
          ),
        );
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: searchController,
                    autofocus: true,
                    decoration: InputDecoration(
                      hintText: "Search...",
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (text) {
                      _filterSuggestions();
                    },
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.search),
                  onPressed: _fetchFilteredActions,
                ),
              ],
            ),
          ),
          // Display search suggestions
          if (suggestions.isNotEmpty)
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    ListView.builder(
                      shrinkWrap: true,
                      itemCount: suggestions.length,
                      itemBuilder: (context, index) {
                        return ListTile(
                          title: Text(suggestions[index]),
                          onTap: () {
                            searchController.text = suggestions[index];
                            _fetchFilteredActions();
                          },
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),

          if (isLoading)
            const Expanded(
              child: Center(
                child: CircularProgressIndicator(),
              ),
            )
          else
            Expanded(
              child: filteredActions.isEmpty
                ? const Center(
                    child: Text('No stocks found'),
                  )
                : ListView.builder(
                    itemCount: filteredActions.length,
                    itemBuilder: (context, index) {
                      final action = filteredActions[index];
                      return StockTile(
                        key: ValueKey(action.name),
                        action: action,
                        onTap: () {
                          BlocProvider.of<MarketBloc>(context).add(
                            ChangeStateWithTransition(
                              stateNext: StateApp.actionDetails,
                              stateNow: StateApp.market,
                              action: action,
                            ),
                          );
                        },
                      );
                    },
                  ),
            ),
        ],
      ),
    );
  }
}
