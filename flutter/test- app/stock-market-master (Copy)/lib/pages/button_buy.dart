import 'package:flutter/material.dart';
import 'package:stock_market/class/action.dart';
import 'package:stock_market/class/user.dart';
import 'package:stock_market/db/db_shared.dart';
import 'package:stock_market/db/db_sql.dart';
import 'package:stock_market/show_message.dart';
import 'package:stock_market/theme/app_colors.dart';
import 'package:stock_market/theme/app_dimensions.dart';
import 'package:stock_market/theme/app_text_styles.dart';
import 'package:stock_market/widgets/styled_components.dart';

class ButtonBuy extends StatelessWidget {
  final ActionStock action;
  final Function()? updateCountAction;
  const ButtonBuy({required this.action, this.updateCountAction, super.key});

  // Function to buy actions
  Future<void> buyActions(int quantityBuy, BuildContext context) async {
    double price = quantityBuy * action.rate;
    bool walletGood = await userHaveEnoughMoney(price);

    if (!walletGood) {
      if (context.mounted) {
        showSnackBar(
          "You have not enough money!",
          Colors.red,
          context,
        );
      }
      return;
    }

    await manageWalletUser(plus: false, price: price);

    try {
      ActionDB actionToSave = ActionDB(
        name: action.name,
        count: quantityBuy,
        price: action.rate,
      );

      await saveAction(action: actionToSave);

      if (updateCountAction != null) {
        updateCountAction!();
      }

      if (context.mounted) {
        showSnackBar(
          "$quantityBuy ${action.name} action(s) bought for ${price.toStringAsFixed(2)}. Congratulations!",
          Colors.green,
          context,
        );
      }
    } catch (e) {
      if (context.mounted) {
        showSnackBar(
          "Error: Purchase failed!",
          Colors.red,
          context,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: AppColors.secondaryGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusMD),
        boxShadow: [
          BoxShadow(
            color: AppColors.secondary.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showBuyDialog(context),
          borderRadius: BorderRadius.circular(AppDimensions.radiusMD),
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingLG,
              vertical: AppDimensions.paddingSM,
            ),
            child: Text(
              "Buy",
              style: AppTextStyles.labelLarge.copyWith(
                color: AppColors.textOnPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showBuyDialog(BuildContext context) {
    TextEditingController quantityController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusLG),
          ),
          backgroundColor: AppColors.surface,
          title: Text(
            "Buy ${action.name}",
            style: AppTextStyles.headlineSmall,
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "How many shares would you like to buy?",
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: AppDimensions.spaceLG),
              StyledTextField(
                controller: quantityController,
                labelText: "Quantity",
                hintText: "Enter number of shares",
                keyboardType: TextInputType.number,
                prefixIcon: const Icon(Icons.numbers),
              ),
              const SizedBox(height: AppDimensions.spaceMD),
              Container(
                padding: const EdgeInsets.all(AppDimensions.paddingMD),
                decoration: BoxDecoration(
                  color: AppColors.surfaceVariant,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusSM),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Price per share:",
                      style: AppTextStyles.bodyMedium,
                    ),
                    Text(
                      "\$${action.rate.toStringAsFixed(2)}",
                      style: AppTextStyles.titleMedium.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                "Cancel",
                style: AppTextStyles.labelLarge.copyWith(
                  color: AppColors.error,
                ),
              ),
            ),
            const SizedBox(width: AppDimensions.spaceSM),
            StyledGradientButton(
              text: "Buy Now",
              gradientColors: AppColors.secondaryGradient,
              onPressed: () async {
                int quantity = int.tryParse(quantityController.text) ?? 0;
                if (quantity > 0) {
                  await buyActions(quantity, context);

                  if (context.mounted) {
                    Navigator.pop(context); // Close the dialog after purchase
                  }
                } else {
                  // If the quantity is invalid, show an error message
                  if (context.mounted) {
                    showSnackBar(
                      "Please enter a valid number of shares!",
                      Colors.red,
                      context,
                    );
                  }
                }
              },
              icon: Icons.shopping_cart,
            ),
          ],
        );
      },
    );
  }
}

Future<bool> userHaveEnoughMoney(double price) async {
  String cookie = await loadUserCookie();
  List<User> users = await loadUserData(user: cookie);
  User user = users.first;

  if (user.wallet < price) {
    return false;
  }

  return true;
}
