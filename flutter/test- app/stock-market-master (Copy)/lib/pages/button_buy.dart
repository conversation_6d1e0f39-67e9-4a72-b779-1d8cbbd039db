import 'package:flutter/material.dart';
import 'package:stock_market/class/action.dart';
import 'package:stock_market/class/user.dart';
import 'package:stock_market/db/db_shared.dart';
import 'package:stock_market/db/db_sql.dart';
import 'package:stock_market/show_message.dart';

class ButtonBuy extends StatelessWidget {
  final ActionStock action;
  final Function()? updateCountAction;
  const ButtonBuy({required this.action, this.updateCountAction, super.key});

  // Function to buy actions
  Future<void> buyActions(int quantityBuy, BuildContext context) async {
    double price = quantityBuy * action.rate;
    bool walletGood = await userHaveEnoughMoney(price);

    if (!walletGood) {
      showSnackBar(
        "You have not enough money!",
        Colors.red,
        context,
      );
    }

    await manageWalletUser(plus: false, price: price);

    try {
      ActionDB actionToSave = ActionDB(
        name: action.name,
        count: quantityBuy,
        price: action.rate,
      );

      await saveAction(action: actionToSave);

      if (updateCountAction != null) {
        updateCountAction!();
      }

      if (context.mounted) {
        showSnackBar(
          "$quantityBuy ${action.name} action(s) bought for ${price.toStringAsFixed(2)}. Congratulations!",
          Colors.green,
          context,
        );
      }
    } catch (e) {
      if (context.mounted) {
        showSnackBar(
          "Error: Purchase failed!",
          Colors.red,
          context,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () => _showBuyDialog(context),
      style: ElevatedButton.styleFrom(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        backgroundColor: Colors.lightGreen,
        elevation: 4,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
      child: const Text(
        "Buy",
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }

  void _showBuyDialog(BuildContext context) {
    TextEditingController quantityController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: const Text(
            "Buy actions",
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text("Enter numbers actions :"),
              const SizedBox(height: 10),
              TextField(
                controller: quantityController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: "Ex: 10",
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text("Cancel", style: TextStyle(color: Colors.red)),
            ),
            ElevatedButton(
              onPressed: () async {
                int quantity = int.tryParse(quantityController.text) ?? 0;
                if (quantity > 0) {
                  await buyActions(quantity, context);

                  Navigator.pop(context); // Close the dialog after purchase
                } else {
                  // If the quantity is invalid, show an error message
                  showSnackBar(
                    "Please enter a valid number of actions!",
                    Colors.red,
                    context,
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.lightGreen,
              ),
              child: const Text(
                "Confirm",
                selectionColor: Colors.white,
              ),
            ),
          ],
        );
      },
    );
  }
}

Future<bool> userHaveEnoughMoney(double price) async {
  String cookie = await loadUserCookie();
  List<User> users = await loadUserData(user: cookie);
  User user = users.first;

  if (user.wallet < price) {
    return false;
  }

  return true;
}
